#!/usr/bin/env python3
from curl_cffi import requests
import time
import json
import os
import logging
import datetime
import sys
import random
from EncryptionModule import encrypt_request_body

# Интервал проверки доступных слотов через API (в секундах)
SLOT_CHECK_INTERVAL = 15
# Таймаут для запросов к API VFS (в секундах)
REQUEST_TIMEOUT = 45
# URL вебхука для Slack оповещений
SLACK_WEBHOOK_URL = "*********************************************************************************"
DASHBOARD_URL = "https://atlantis-absapi-ru.vfsglobal.com/v1/applications/dashboard"

# Настройка логирования в файл
log_dir = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_dir, exist_ok=True)
start_time = datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
log_file = os.path.join(log_dir, f'run_{start_time}.log')
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    filename=log_file,
    encoding='utf-8'
)

# Правила поиска дат
DATE_RULES = {
    'start_date': datetime.date(2025, 5, 1),  # первая допустимая дата
    'end_date':   datetime.date(2025, 7, 15), # последняя допустимая дата
    'min_days':   0,                          # минимальный отступ от сегодня
    'exclude_dates': []
}

# --- Интерактивный выбор конфигурации ---
while True:
    try:
        account_num = input("Введите номер аккаунта (от 0 до 16): ")
        if not account_num.isdigit() or int(account_num) < 0 or int(account_num) > 16:
            raise ValueError("Неверный номер аккаунта.")
        CONFIG_PATH = os.path.join(os.path.dirname(__file__), 'AccountsInWork', f'{account_num}.json')
        with open(CONFIG_PATH, 'r', encoding='utf-8') as f:
            config = json.load(f)
        break # Выходим из цикла, если все успешно
    except FileNotFoundError:
        print(f"Ошибка: Файл конфигурации {CONFIG_PATH} не найден.")
    except ValueError as e:
        print(f"Ошибка: {e}")
    except Exception as e:
        print(f"Непредвиденная ошибка при загрузке конфигурации: {e}")
        # Можно добавить sys.exit(1) если критично

# --- Загрузка данных из выбранного конфига ---
AUTH_DATA = config['auth_data']
SLOT_PAYLOAD_TEMPLATE = config['booking_payload']
PROXY = config.get('proxy')
MONITORING_ONLY = config.get('monitoringOnly', False)

# Параметры для функций
mission_code = SLOT_PAYLOAD_TEMPLATE['missionCode']
country_code = SLOT_PAYLOAD_TEMPLATE['countryCode']
vac_code = SLOT_PAYLOAD_TEMPLATE['vacCode']
visa_category = SLOT_PAYLOAD_TEMPLATE['visaSubtype']
gwf_number = SLOT_PAYLOAD_TEMPLATE['gwfNumber']
user_name = SLOT_PAYLOAD_TEMPLATE['userName']

# --- Настройка логгера для добавления GWF ---
log_format_with_gwf = f'[%(asctime)s] [{gwf_number}] %(levelname)s: %(message)s'
formatter = logging.Formatter(log_format_with_gwf, datefmt='%Y-%m-%d %H:%M:%S')
root_logger = logging.getLogger()
for handler in root_logger.handlers:
    handler.setFormatter(formatter)

logging.info(f"Скрипт запущен для аккаунта {gwf_number}") # Пример лога с GWF

# --- Функция для отправки оповещений в Slack ---
def send_slack_notification(valid_slots, city_code):
    if not SLACK_WEBHOOK_URL:
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] Slack Webhook URL не задан, пропуск оповещения.")
        return
    if not valid_slots:
        return # Не отправляем, если список пуст

    message_lines = [f"🔔 Найдены слоты для GWF {gwf_number} в городе {city_code}:"]
    for slot in valid_slots:
        date = slot.get('slotAllocatedDate')
        start_time = slot.get('actualStartTime', '?') # Используем actualStartTime, если есть
        message_lines.append(f"  - {date} {start_time}")

    payload = {"text": "\n".join(message_lines)}
    try:
        # Используем тот же requests, что и основной скрипт
        response = requests.post(SLACK_WEBHOOK_URL, json=payload, timeout=10)
        if response.status_code == 200:
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] Slack-оповещение успешно отправлено.")
            logging.info("Slack-оповещение успешно отправлено.")
        else:
            error_msg = f"Ошибка отправки Slack-оповещения: {response.status_code} - {response.text}"
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {error_msg}")
            logging.error(error_msg)
    except Exception as e:
        error_msg = f"Исключение при отправке Slack-оповещения: {e}"
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {error_msg}")
        logging.exception("Исключение при отправке Slack-оповещения")

# --- Кастомное исключение для остановки при успехе ---
class BookingSuccessException(Exception):
    pass

def get_access_token(session):
    auth_url = "https://keycloak.vfsglobal.com/realms/vfs_realm/protocol/openid-connect/token"
    response = session.post(auth_url, data=AUTH_DATA, impersonate='firefox135', timeout=REQUEST_TIMEOUT)
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        raise Exception(f"Authentication failed: {response.status_code} - {response.text}")


def get_navigation_data(session, access_token, gwf_number):
    nav_url = "https://atlantis-absapi-ru.vfsglobal.com/v1/applications/navigation"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
        "Accept": "application/json, text/plain, */*",
        "Origin": "https://atlantis-abs-uk-ru.vfsglobal.com",
        "Referer": "https://atlantis-abs-uk-ru.vfsglobal.com/"
    }
    payload = {
        "GWFNumber": gwf_number,
        "AccessToken": access_token
    }
    response = session.post(nav_url, json=payload, headers=headers, impersonate='firefox135', timeout=REQUEST_TIMEOUT)
    if response.status_code == 200:
        msg = "Navigation data retrieved successfully"
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg}")
        logging.info(msg)
        return response.json()
    else:
        raise Exception(f"Navigation request failed: {response.status_code} - {response.text}")


def get_service_level_capping(session, access_token, mission_code, country_code, vac_code, visa_category, gwf_number):
    capping_url = "https://atlantis-absapi-ru.vfsglobal.com/v1/applications/capping/serviceLevel"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0",
        "Accept": "application/json, text/plain, */*",
        "Origin": "https://atlantis-abs-uk-ru.vfsglobal.com",
        "Referer": "https://atlantis-abs-uk-ru.vfsglobal.com/"
    }
    payload = {
        "missionCode": mission_code,
        "countryCode": country_code,
        "vacCode": vac_code,
        "visaCategory": visa_category,
        "AccessToken": access_token,
        "gwfNumber": gwf_number
    }
    response = session.post(capping_url, json=payload, headers=headers, impersonate='firefox135', timeout=REQUEST_TIMEOUT)
    if response.status_code == 200:
        msg = "Service level capping data retrieved successfully"
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg}")
        logging.info(msg)
        return response.json()
    else:
        raise Exception(f"Service level capping request failed: {response.status_code} - {response.text}")


def check_slot_availability(session, access_token, payload):
    abs_url = "https://atlantis-absapi-ru.vfsglobal.com/v1/applications/abs/absdetails"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0",
        "Accept": "application/json, text/plain, */*",
        "Origin": "https://atlantis-abs-uk-ru.vfsglobal.com",
        "Referer": "https://atlantis-abs-uk-ru.vfsglobal.com/"
    }
    response = session.post(abs_url, json=payload, headers=headers, impersonate='firefox135', timeout=REQUEST_TIMEOUT)
    if response.status_code == 200:
        data = response.json()
        # data = {
        #     "absMonths": ["May 2025"],
        #     "absDates": ["2025-05-20"],
        #     "appointmentServices": [
        #         {
        #             "calendarTypeId": 2,
        #             "serviceId": 0,
        #             "serviceCode": None,
        #             "serviceName": "Standard (Assisted)",
        #             "amount": None,
        #             "currencyName": None
        #         }
        #     ],
        #     "earliestAvailableSlot": [
        #         {
        #             "calendarTypeId": 2,
        #             "appointmentServiceId": None,
        #             "visaGroupId": None,
        #             "availableDate": "5/20/2025",
        #             "slotStartTime": "9:15 AM"
        #         },
        #         {
        #             "calendarTypeId": 1,
        #             "appointmentServiceId": None,
        #             "visaGroupId": None,
        #             "availableDate": "",
        #             "slotStartTime": None
        #         },
        #         {
        #             "calendarTypeId": 3,
        #             "appointmentServiceId": None,
        #             "visaGroupId": None,
        #             "availableDate": "",
        #             "slotStartTime": None
        #         }
        #     ],
        #     "availableSlot": [
        #         {
        #             "calendarTypeId": 2,
        #             "appointmentServiceId": None,
        #             "sequenceNo": 1,
        #             "actualTimeBandId": 12053,
        #             "noOfSlots": 2,
        #             "slotAllocatedDate": "2025-05-20",
        #             "actualStartTime": "9:15 AM",
        #             "actualEndTime": "9:30 AM",
        #             "slotStartTime": "9:00 AM",
        #             "slotEndTime": "10:00 AM"
        #         }
        #     ],
        #     "csl5Slots": {
        #         "calendarTypeId": 2,
        #         "availableDate": "2025-05-20T00:00:00",
        #         "slotStartTime": "2025-05-20T09:15:00"
        #     },
        #     "error": None
        # }

        if data.get("code") == 5002:
            msg = "No slots available"
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg}")
            logging.info(msg)
            return False
        else:
            msg_print = f"Slots may be available: {json.dumps(data, ensure_ascii=False)}"
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg_print}")
            # В лог-файл пишем полный JSON
            msg_log = f"Slots found: {json.dumps(data, ensure_ascii=False)}"
            logging.info(msg_log)
            return data
    else:
        msg = f"Slot check failed: {response.status_code} - {response.text}"
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg}")
        logging.error(msg)
        return False


def play_sound():
    print('\a')
    os.system('say "Checking slots"')


def date_in_range(date_str, rules):
    slot_date = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
    today = datetime.date.today()
    if (slot_date - today).days < rules['min_days']:
        return False
    if slot_date < rules['start_date'] or slot_date > rules['end_date']:
        return False
    if slot_date in rules['exclude_dates']:
        return False
    return True


def book_appointment(session, access_token, payload):
    url_pay = 'https://atlantis-absapi-ru.vfsglobal.com/v1/applications/payment/process'
    logging.info('Raw booking payload: %s', json.dumps(payload, ensure_ascii=False))
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] [RAW PAYLOAD] {json.dumps(payload, ensure_ascii=False, indent=2)}")
    encrypted = encrypt_request_body(url_pay, payload)
    #logging.info('Encrypted booking payload: %s', json.dumps(encrypted, ensure_ascii=False))
    #print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] [ENCRYPTED PAYLOAD] {json.dumps(encrypted, ensure_ascii=False, indent=2)}")
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0',
        'Accept': 'application/json, text/plain, */*'
    }
    resp = session.post(url_pay, json=encrypted, headers=headers, impersonate='firefox135', timeout=REQUEST_TIMEOUT)
    logging.info('Booking HTTP status %s, response: %s', resp.status_code, resp.text)
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] [RESPONSE STATUS {resp.status_code}] {resp.text}")
    if resp.status_code != 200:
        logging.error('Booking error %s: %s', resp.status_code, resp.text)
        return None
    h = resp.json()
    logging.info('Booking response: %s', json.dumps(h, ensure_ascii=False))
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] [PARSED RESPONSE] {json.dumps(h, ensure_ascii=False, indent=2)}")

    # --- Анализ ответа (скопировано из grok_sol2.py) ---
    code = h.get('code')
    pg = h.get('pgDetails') or {}
    # Специальная обработка кода 6057: слот недоступен, возвращаем None для продолжения сканирования
    if code == 6057:
        msg = 'Слот больше недоступен (6057)'
        logging.warning(msg)
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] [WARNING] {msg}")
        return None
    if code == 2007:
        msg = 'Платёжный шлюз недоступен (code 2007)'
        logging.warning(msg)
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] [INFO] {msg}")
    elif h.get('error') is None and not pg.get('url'):
        msg = 'Переход на страницу статуса без оплаты'
        logging.info(msg)
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] [INFO] {msg}")
        raise BookingSuccessException(msg)
    elif h.get('error') is None and pg.get('url'):
        msg = f'Перенаправление на платёжный шлюз: {pg["url"]}'
        logging.info(msg)
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] [INFO] {msg}")
        raise BookingSuccessException(msg)
    else:
        err = h.get('error', {})
        err_code = err.get('code')
        if resp.status_code == 400 and err_code == 6057:
            msg = 'Слот больше недоступен (6057)'
            logging.error(msg)
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] [ERROR] {msg}")
        else:
            msg = f'Неизвестная ошибка бронирования: {err}'
            logging.error(msg)
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] [ERROR] {msg}")
    # --- Конец анализа ответа ---
    return h


def build_booking_payload(slot, token, check_payload, date_of_booking=None):
    """Формирует словарь payload для бронирования."""
    dob_str = datetime.datetime.utcnow().isoformat(timespec='milliseconds') + 'Z'
    slot_payload = {
        'appointmentServiceId': check_payload.get('appointmentServiceId', 0),
        'appointmentServiceName': check_payload.get('appointmentServiceName', 'Standard Assisted'),
        'dateOfBooking': dob_str,
        'actualTimeBandId': slot['actualTimeBandId'],
        'appointmentDate': slot['slotAllocatedDate'],
        'startTime': slot['actualStartTime'],
        'endTime': slot['actualEndTime'],
        'appointmentStateId': 1,
        'calenderTypeId': slot['calendarTypeId'],
        'isConfirmed': 0,
        'action': 'schedule',
        'serviceLevelId': check_payload['serviceLevelId'],
    }
    payload = {
        'loggedInUser': user_name,
        'IsForBuyAdditionalService': False,
        'IsForPaymentRetry': False,
        'PaymentMode': 1,
        'missionCode': mission_code,
        'countryCode': country_code,
        'vacCode': vac_code,
        'missionId': str(check_payload['missionId']),
        'countryId': check_payload['countryId'],
        'vacId': check_payload['vacId'],
        'applicantUan': check_payload['ApplicantUan'],
        'applicantId': 1,
        'AccessToken': token,
        'phoneNumber': check_payload['phoneNumber'],
        'shippingPhoneNumber': '',
        'visaCategoryId': check_payload['visaCategoryId'],
        'visaCategoryCode': check_payload.get('visaSubtype', visa_category),
        'noShow': False,
        'applicantGroupUAN': check_payload.get('applicantGroupUAN', gwf_number),
        'isCancelledReBooking': False,
        'isApptConfirmed': False,
        'isReschedule': False,
        'myProperty': 0,
        'languageCode': 'en-US',
        'applicantVac': {
            'vacCode': vac_code,
            'fees': None,
            'penaltyFee': None,
            'supportIds': []
        },
        'applicantServiceLevel': {
            'serviceLevel': check_payload['serviceLevelId'],
            'ServiceLevelCode': check_payload['serviceLevel'],
            'fees': None
        },
        'slot': [slot_payload],
        'avs': check_payload.get('avs', {
            'avsGroup': [
                {
                    'id': 1,
                    'isMandatory': False,
                    'avsService': [
                        {
                            'serviceId': 6,
                            'serviceName': 'Document Self Upload',
                            'serviceCode': 'SCANUPLOAD',
                            'serviceType': 'AVS',
                            'isCourierService': 0,
                            'unitPrice': '0.00',
                            'unit': 1,
                            'currencyCode': 'RUB',
                            'freeService': 1,
                            'isPackage': 0,
                            'avsTax': [
                                {'taxPercentage': '0.00', 'taxTypeCode': 'CGST'},
                                {'taxPercentage': '0.00', 'taxTypeCode': 'SGST'}
                            ],
                            'includedAvs': None
                        }
                    ]
                }
            ],
            'RemovedServiceIDs': None
        }),
        'visaReservation': None,
        'courier': check_payload.get('courier', {'courierType': 'Courier', 'courierPartnerId': 0, 'isHalAddress': False, 'isCourier': 0}),
        'deliveryAddress': check_payload.get('deliveryAddress', {'addressline1':'','addressline2':'','city':'','state':'','postalCode':'','country':''}),
        'odmv': check_payload.get('odmv', {'deliveryAddress': {'addressline1':'','addressline2':'','city':'','state':'','postalCode':''}, 'isODMV': False})
    }
    return payload


def get_dashboard_data(session, access_token):
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "en-US,en;q=0.9",
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "Origin": "https://atlantis-abs-uk-ru.vfsglobal.com",
        "Priority": "u=1, i",
        "Referer": "https://atlantis-abs-uk-ru.vfsglobal.com/",
        "Sec-CH-UA": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
        "Sec-CH-UA-Mobile": "?0",
        "Sec-CH-UA-Platform": "\"Windows\"",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-site",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    payload = {
        "missionCode": mission_code,
        "countryCode": country_code,
        "vacCode": vac_code,
        "gwfNumber": gwf_number,
        "AccessToken": access_token
    }
    response = session.post(DASHBOARD_URL, json=payload, headers=headers, impersonate='firefox135', timeout=REQUEST_TIMEOUT)
    if response.status_code == 200:
        return response.json()
    else:
        logging.warning(f"Dashboard request failed: {response.status_code} - {response.text}")
        return {}


def full_check_cycle(session):
    try:
        token = get_access_token(session)
        msg = "Token obtained"
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg}")
        logging.info(msg)

        # Навигация и проверка уровня сервиса
        get_navigation_data(session, token, gwf_number)
        get_service_level_capping(session, token, mission_code, country_code, vac_code, visa_category, gwf_number)
        # Проверяем существующую запись через dashboard endpoint после установления сессии
        dashboard_data = get_dashboard_data(session, token)
        # # Безопасно извлекаем dashboardPageLoadData, учитывая возможный None
        dp = dashboard_data.get("dashboardPageLoadData") or {}
        appointment = dp.get("appointmentDetail")
        pass
        # Проверяем, что вернулась не placeholder запись с датой '0001-01-01'
        appointment_date = appointment.get('appointmentDate') if appointment else None
        if appointment and appointment_date and appointment_date != '0001-01-01':
            customer = dp.get('customerDetail', {})
            addr = appointment.get('vacAddress', {})
            addr_str = ', '.join(filter(None, [addr.get('line1',''), addr.get('line2',''), addr.get('line3',''), addr.get('townCity','')]))
            date = appointment_date
            time_str = customer.get('appointmentTime', '')
            msg2 = f"Существующая запись: {addr_str} | {date} {time_str}"
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg2}")
            logging.info(msg2)
            if not MONITORING_ONLY:
                # Останавливаем скрипт при наличии реальной записи
                raise BookingSuccessException(msg2)
        check_payload = dict(SLOT_PAYLOAD_TEMPLATE)
        check_payload['AccessToken'] = token
        slots = check_slot_availability(session, token, check_payload)
        if slots:
            # --- Отправка Slack оповещения при нахождении слотов --- 
            valid_slots_for_notification = [
                s for s in slots.get('availableSlot', [])
                if date_in_range(s.get('slotAllocatedDate'), DATE_RULES)
            ]
            if valid_slots_for_notification:
                send_slack_notification(valid_slots_for_notification, vac_code)
            
            # --- Перемешиваем слоты перед бронированием --- 
            available_slots_list = slots.get('availableSlot', [])
            random.shuffle(available_slots_list)
            # --- Конец перемешивания ---

            # --- Конец отправки Slack оповещения ---

            # Пробуем забронировать каждый доступный слот до первого успешного
            for slot in available_slots_list: # Итерируем по перемешанному списку
                d = slot.get('slotAllocatedDate')
                if not date_in_range(d, DATE_RULES):
                    continue
                logging.info('Found slot on %s, attempting booking', d)
                booking_payload = build_booking_payload(slot, token, check_payload)
                result = book_appointment(session, token, booking_payload)
                if result and not result.get('error'):
                    logging.info('Booking succeeded for slot %s', d)
                    break
                else:
                    logging.warning('Booking failed for slot %s, trying next', d)
        return slots, token
    except BookingSuccessException:
        # Пробрасываем, чтобы остановить скрипт в main
        raise
    except Exception as e:
        msg = f"Error in full_check_cycle: {e}"
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg}")
        logging.error(msg)
        return False, None


def main():
    session = requests.Session()
    # Настраиваем прокси из конфига, если указан
    if PROXY:
        session.proxies = {'http': f'http://{PROXY}', 'https': f'http://{PROXY}'}
    msg_start = "Starting VFS slot scanner via JSON config..."
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg_start}")
    logging.info(msg_start)
    while True:
        try:
            msg_cycle = "New cycle with full auth"
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg_cycle}")
            logging.info(msg_cycle)
            slots_data, token = full_check_cycle(session)
            if not token:
                continue
            # Инициализация счётчика специальных попыток перед циклом
            special_attempts = 0
            for i in range(30):
                now = datetime.datetime.now()
                # Режим ожидания 21:30:00, активируется после 21:29:30
                if special_attempts == 0 and now.hour == 21 and now.minute == 29 and now.second >= 30:
                    target = now.replace(hour=21, minute=30, second=0, microsecond=0)
                    interval = (target - now).total_seconds()
                    special_attempts = 1
                    # Логируем паузу сканирования и отправляем уведомление в Slack
                    msg_pause = f"⏸ Приостановка сканирования до {target.strftime('%H:%M:%S')}"
                    logging.info(msg_pause)
                    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg_pause}")
                    try:
                        payload_slack = {"text": msg_pause}
                        requests.post(SLACK_WEBHOOK_URL, json=payload_slack, timeout=10)
                    except Exception as e:
                        logging.exception("Ошибка отправки Slack-уведомления о паузе")
                # Первая спец. попытка в 21:30 сразу
                elif special_attempts == 1 and now.hour == 21 and now.minute == 30:
                    interval = 0
                    special_attempts = 2
                # Вторая спец. попытка через 5 секунд после первой
                elif special_attempts == 2 and now.hour == 21 and now.minute == 30:
                    interval = 5
                    special_attempts = 3
                else:
                    interval = SLOT_CHECK_INTERVAL
                msg_check = f"Check {i+2}/20, waiting {interval}s..."
                print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg_check}")
                time.sleep(interval)
                check_payload = dict(SLOT_PAYLOAD_TEMPLATE)
                check_payload['AccessToken'] = token
                slots_data = check_slot_availability(session, token, check_payload)
                if slots_data:
                    # --- Отправка Slack оповещения при нахождении слотов --- 
                    valid_slots_for_notification_main = [
                        s for s in slots_data.get('availableSlot', [])
                        if date_in_range(s.get('slotAllocatedDate'), DATE_RULES)
                    ]
                    if valid_slots_for_notification_main:
                        send_slack_notification(valid_slots_for_notification_main, vac_code)
                    
                    # --- Перемешиваем слоты перед бронированием --- 
                    available_slots_list_main = slots_data.get('availableSlot', [])
                    random.shuffle(available_slots_list_main)
                    # --- Конец перемешивания ---

                    # --- Конец отправки Slack оповещения ---

                    for slot in available_slots_list_main: # Итерируем по перемешанному списку
                        d = slot.get('slotAllocatedDate')
                        if date_in_range(d, DATE_RULES):
                            logging.info('Found slot on %s, booking', d)
                            booking_payload = build_booking_payload(slot, token, check_payload)
                            result = book_appointment(session, token, booking_payload)
                            # Если book_appointment выбросит BookingSuccessException, мы сюда не дойдем
                            # Если бронь не удалась, но не исключение (напр. 6057), продолжаем цикл

        except BookingSuccessException as success_info:
            print(f"\n==================================================")
            print(f"УСПЕШНОЕ БРОНИРОВАНИЕ для {gwf_number}!")
            print(f"Статус: {success_info}")
            print(f"Скрипт завершает работу.")
            print(f"==================================================\n")
            sys.exit(0) # Успешный выход
        except Exception as e:
            # Ловим другие возможные ошибки основного цикла
            msg_crit = f"Критическая ошибка в основном цикле: {e}"
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] [{gwf_number}] {msg_crit}")
            logging.exception("Критическая ошибка в основном цикле")
            time.sleep(5) # Пауза перед рестартом цикла


if __name__ == "__main__":
    main() 