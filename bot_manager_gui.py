import tkinter as tk
from tkinter import ttk
from tkinter import scrolledtext
import subprocess
import threading
import queue
import os
import sys
import logging
import time # Добавлено для time.time()
import json
from datetime import datetime
import re

# Попытка импортировать psutil для статистики RAM
try:
    import psutil
    psutil_available = True
except ImportError:
    psutil_available = False

# Добавляем модули для массового запуска и получения данных заявок
# import mass_start  # временно удалено, batch-функции недоступны
import main
from tkinter import messagebox
from functions.essentials import send_slack_error # Добавляем импорт

class BotManagerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("VFS England")
        self.root.geometry("1200x700")

        # Основные структуры данных
        self.processes = {}
        self.global_output_queue = queue.Queue()
        self.active_acc_ids_in_list_order = []
        self.selected_acc_id = None

        # НОВЫЕ ПЕРЕМЕННЫЕ ДЛЯ ОПТИМИЗАЦИИ
        self.max_log_lines = 1000  # Максимум строк в логе
        self.gui_update_interval = 200  # Интервал обновления GUI в мс (вместо 100)
        self.output_batch = {}  # Батчевая обработка вывода
        self.last_gui_update = {}  # Время последнего обновления GUI для каждого бота
        self.gui_throttle_interval = 0.1  # Минимальный интервал между обновлениями GUI (100мс)
        self.show_output_for_inactive = True  # Показывать ли вывод для неактивных ботов

        self.create_interface()
        self.process_global_output_queue()
        self.update_statistics_periodically()
        self.check_stuck_processes()

        # Обработка закрытия приложения
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def _get_short_acc_id(self, acc_id_str):
        if acc_id_str.startswith('Z') and acc_id_str[1:].isdigit():
            return acc_id_str[1:]
        elif acc_id_str.startswith('ID_') and acc_id_str[3:].isdigit():
            return acc_id_str[3:]
        return acc_id_str

    def _get_bot_list_display_text(self, acc_id, status_icon=""):
        short_id = self._get_short_acc_id(acc_id)
        # Определяем состояние бота для более детального отображения
        proc_data = self.processes.get(acc_id)
        if proc_data:
            process = proc_data.get('process')
            is_stopped = (process is None or process.poll() is not None) or proc_data.get('_gui_marked_stopped_')
            is_stuck = proc_data.get('_gui_marked_stuck_', False)
            
            if is_stopped:
                return f"■ {short_id} [ОСТАНОВЛЕН]"
            elif is_stuck:
                return f"⚠️ {short_id} [ЗАВИС]"
            elif status_icon:
                return f"{status_icon} {short_id}"
            else:
                return f"▶ {short_id}"
        return f"{status_icon} {short_id}"

    def _update_bot_list_item_display(self, acc_id, status_icon):
        """Обновляет отображение элемента в Listbox для данного acc_id."""
        if acc_id in self.active_acc_ids_in_list_order:
            try:
                idx = self.active_acc_ids_in_list_order.index(acc_id)
                new_display_text = self._get_bot_list_display_text(acc_id, status_icon)
                
                # Сохраняем текущее выделение
                is_selected = self.bot_listbox.selection_includes(idx)
                
                self.bot_listbox.delete(idx)
                self.bot_listbox.insert(idx, new_display_text)
                
                if is_selected:
                    self.bot_listbox.selection_set(idx)
            except ValueError:
                print(f"GUI: Ошибка: acc_id {acc_id} не найден в active_acc_ids_in_list_order при обновлении Listbox.")
            except tk.TclError as e:
                print(f"GUI: TclError при обновлении элемента Listbox для {acc_id}: {e}")

    def on_bot_select(self, event=None):
        """Обрабатывает выбор бота из списка."""
        selection = self.bot_listbox.curselection()
        if not selection:
            return

        # Скрываем текущие виджеты
        for widget in self.console_pane_frame.winfo_children():
            widget.pack_forget()

        # Получаем выбранный acc_id
        selected_index = selection[0]
        selected_acc_id = self.active_acc_ids_in_list_order[selected_index]
        old_selected = self.selected_acc_id
        self.selected_acc_id = selected_acc_id

        proc_data = self.processes[selected_acc_id]
        text_area = proc_data['text_area']
        button_frame = proc_data['button_frame']

        # Показываем виджеты выбранного бота
        text_area.pack(fill=tk.BOTH, expand=True)
        button_frame.pack(fill=tk.X, pady=5)

        # Если переключились на другого бота и включен режим "только активный"
        if not self.show_output_for_inactive and old_selected != selected_acc_id:
            # Принудительно обновляем лог для нового активного бота
            if selected_acc_id in self.output_batch and self.output_batch[selected_acc_id]:
                batch_content = ''.join(self.output_batch[selected_acc_id])
                self.update_text_area(selected_acc_id, batch_content)
                self.output_batch[selected_acc_id] = []
                self.last_gui_update[selected_acc_id] = time.time()

        print(f"Выбран бот: {selected_acc_id}")

    def update_statistics_periodically(self):
        """Периодически обновляет статистику по ботам."""
        running_count = 0
        stopped_count = 0
        total_ram_bytes = 0

        for acc_id, data in list(self.processes.items()): # list() для безопасной итерации при возможном изменении
            process_obj = data.get('process')
            is_stopped = (process_obj is None or process_obj.poll() is not None) or data.get('_gui_marked_stopped_')
            
            if is_stopped:
                stopped_count += 1
            else:
                running_count += 1
                if psutil_available:
                    try:
                        p = psutil.Process(process_obj.pid)
                        total_ram_bytes += p.memory_info().rss
                    except (psutil.NoSuchProcess, psutil.AccessDenied, AttributeError):
                        # AttributeError на случай, если process_obj.pid не существует в редких случаях
                        pass # Process might have just died, or permissions issue

        # Обновляем текст статистики с информацией об остановленных ботах
        stats_text = f"Активных: {running_count}"
        if stopped_count > 0:
            stats_text += f" | Остановлено: {stopped_count}"
        self.stats_label.config(text=stats_text)

        if psutil_available:
            total_ram_mb = total_ram_bytes / (1024 * 1024)
            self.ram_usage_label.config(text=f"RAM: {total_ram_mb:.2f} MB")
        else:
            self.ram_usage_label.config(text="RAM: N/A")

        self.root.after(5000, self.update_statistics_periodically) # Обновлять каждые 5 секунд

    def _update_tab_text(self, acc_id, status_icon):
        """DEPRECATED: Используйте _update_bot_list_item_display. Сохранено для обратной совместимости вызовов, если есть."""
        # Эта функция теперь перенаправляет на новый метод обновления Listbox
        self._update_bot_list_item_display(acc_id, status_icon)

    def start_bot_event(self, event=None):
        """Обработчик события для запуска бота (кнопка или Enter)."""
        acc_id = self.acc_id_entry.get().strip()
        if not acc_id:
            tk.messagebox.showwarning("Пустой ID", "Пожалуйста, введите ID аккаунта.")
            return
        if acc_id in self.processes:
            tk.messagebox.showwarning("Бот уже запущен", f"Бот с ID '{acc_id}' уже запущен.")
            return

        self.start_bot(acc_id)
        self.acc_id_entry.delete(0, tk.END) # Очистить поле ввода

    def start_bot(self, acc_id):
        """Запускает новый процесс бота и создает для него вкладку."""
        print(f"Запуск бота для аккаунта: {acc_id}")

        # Создаем элементы управления для правой панели (консоли)
        # Они будут привязаны к self.console_pane_frame, но показаны/скрыты при выборе
        
        # Добавляем область для вывода текста
        text_area = scrolledtext.ScrolledText(self.console_pane_frame, wrap=tk.WORD, state=tk.DISABLED, width=80, height=25)
        # text_area.pack(expand=True, fill="both", pady=(0, 5)) # Не пакуем сразу

        # Добавляем кнопки управления
        button_frame = ttk.Frame(self.console_pane_frame)
        # button_frame.pack(fill=tk.X) # Не пакуем сразу

        stop_button = ttk.Button(button_frame, text="Стоп", command=lambda: self.stop_bot(acc_id))
        stop_button.pack(side=tk.LEFT, padx=(0, 5))

        restart_button = ttk.Button(button_frame, text="Перезапуск", command=lambda: self.restart_bot(acc_id))
        restart_button.pack(side=tk.LEFT, padx=(0,5))

        close_button = ttk.Button(button_frame, text="Закрыть", command=lambda: self.close_bot_tab(acc_id), state=tk.DISABLED)
        close_button.pack(side=tk.LEFT)
        
        script_path = os.path.abspath(os.path.join(os.path.dirname(__file__), 'main.py'))  # Используем абсолютный путь
        if not os.path.exists(script_path):
             tk.messagebox.showerror("Ошибка", f"Скрипт 'main.py' не найден в директории {os.path.dirname(__file__)}")
             # self.notebook.forget(tab) # Удаляем вкладку, если скрипт не найден
             return
        
        # Отображаем информацию по заявке в консоли вкладки (сразу в text_area)
        order_id = acc_id[1:] if acc_id.startswith('Z') else acc_id
        
        # Запускаем получение деталей заказа в отдельном потоке
        threading.Thread(target=self.fetch_and_display_order_details, args=(acc_id, order_id, text_area), daemon=True).start()

        # Создаем очередь для вывода
        # output_queue = queue.Queue()
        # self.output_queues[acc_id] = output_queue

        try:
            # Запускаем main.py как дочерний процесс
            # Используем sys.executable для запуска Python
            process = subprocess.Popen(
                [sys.executable, '-u', script_path, acc_id], # '-u' для небуферизованного вывода
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE, # Добавлено для возможности ввода, если понадобится
                text=True,
                encoding='utf-8', # Явно указываем кодировку
                errors='replace', # Заменяем ошибки кодировки
                creationflags=subprocess.CREATE_NO_WINDOW, # Не создавать окно консоли в Windows
                bufsize=1,  # Строчная буферизация
                cwd=os.path.dirname(script_path)  # Запуск из директории проекта для корректного импорта
            )

            # Сохраняем информацию о процессе
            current_time = time.time()
            self.processes[acc_id] = {
                'process': process,
                # 'tab': tab, # Больше не используется
                'text_area': text_area,
                'button_frame': button_frame, # Родительский фрейм для кнопок
                'stop_button': stop_button,
                'restart_button': restart_button,
                'close_button': close_button,
                '_gui_marked_stopped_': False,
                '_gui_marked_stuck_': False,
                'last_output_time': current_time
            }
            
            # Добавляем в Listbox и active_acc_ids_in_list_order
            display_text = self._get_bot_list_display_text(acc_id, "▶")
            self.bot_listbox.insert(tk.END, display_text)
            self.active_acc_ids_in_list_order.append(acc_id)
            
            # Выбираем нового бота в списке
            new_bot_idx = self.bot_listbox.size() - 1
            self.bot_listbox.selection_clear(0, tk.END)
            self.bot_listbox.select_set(new_bot_idx)
            self.on_bot_select() # Обновляем правую панель

            # Запускаем потоки для чтения stdout и stderr
            threading.Thread(target=self.read_output, args=(process.stdout, acc_id), daemon=True).start()
            threading.Thread(target=self.read_output, args=(process.stderr, acc_id), daemon=True).start()

            # Глобальный обработчик очереди уже запущен в __init__

            # Переключаемся на новую вкладку - теперь это выбор в Listbox
            # self.notebook.select(tab) 
            # Обновляем GUI и временно поднимаем главное окно сверху для отображения вкладки
            try:
                self.root.update_idletasks()
                # Включаем режим поверх всех окон и сразу выключаем его
                self.root.attributes('-topmost', True)
                self.root.attributes('-topmost', False)
            except Exception:
                pass

        except Exception as e:
            tk.messagebox.showerror("Ошибка Запуска", f"Не удалось запустить бота: {e}")
            # self.notebook.forget(tab) # Удаляем вкладку при ошибке
            if text_area.winfo_exists(): text_area.destroy()
            if button_frame.winfo_exists(): button_frame.destroy()
            # if acc_id in self.output_queues:
            #     del self.output_queues[acc_id]
            if acc_id in self.processes: # Дополнительная проверка перед удалением
                 del self.processes[acc_id]

    def read_output(self, pipe, acc_id):
        """Читает вывод из потока (stdout/stderr) и помещает в батч для обработки."""
        try:
            for line in iter(pipe.readline, ''):
                # Инициализируем батч для этого бота если нужно
                if acc_id not in self.output_batch:
                    self.output_batch[acc_id] = []
                
                # Добавляем строку в батч
                self.output_batch[acc_id].append(line)
                
                # Если батч стал слишком большим, отправляем в очередь
                if len(self.output_batch[acc_id]) >= 50:  # Батчи по 50 строк
                    batch_content = ''.join(self.output_batch[acc_id])
                    self.global_output_queue.put((acc_id, batch_content))
                    self.output_batch[acc_id] = []
                    
        except ValueError as e:
            print(f"Поток вывода для {acc_id} был закрыт: {e}")
        except Exception as e:
            print(f"Ошибка при чтении потока {acc_id}: {e}")
        finally:
            # Отправляем оставшиеся строки в батче
            if acc_id in self.output_batch and self.output_batch[acc_id]:
                batch_content = ''.join(self.output_batch[acc_id])
                self.global_output_queue.put((acc_id, batch_content))
                del self.output_batch[acc_id]
            if pipe:
                pipe.close()

    def process_global_output_queue(self):
        """Обрабатывает глобальную очередь вывода всех ботов с throttling."""
        processed_count = 0
        max_items_per_cycle = 20  # Ограничиваем количество обработанных элементов за цикл
        
        try:
            while processed_count < max_items_per_cycle:
                acc_id, text = self.global_output_queue.get_nowait()
                if text is not None:
                    # Проверяем нужно ли показывать вывод для этого бота
                    should_show = self.show_output_for_inactive or (acc_id == self.selected_acc_id)
                    
                    if should_show:
                        # Проверяем throttling
                        current_time = time.time()
                        last_update = self.last_gui_update.get(acc_id, 0)
                        
                        if current_time - last_update >= self.gui_throttle_interval:
                            self.update_text_area(acc_id, text)
                            self.last_gui_update[acc_id] = current_time
                        # Если слишком рано для обновления, пропускаем (данные теряются для производительности)
                    
                processed_count += 1
        except queue.Empty:
            pass
            
        # Отправляем оставшиеся батчи если прошло достаточно времени
        current_time = time.time()
        for acc_id, batch_lines in list(self.output_batch.items()):
            if batch_lines:  # Если есть накопленные строки
                last_update = self.last_gui_update.get(acc_id, 0)
                if current_time - last_update >= 1.0:  # Принудительно отправляем каждую секунду
                    batch_content = ''.join(batch_lines)
                    should_show = self.show_output_for_inactive or (acc_id == self.selected_acc_id)
                    if should_show:
                        self.update_text_area(acc_id, batch_content)
                        self.last_gui_update[acc_id] = current_time
                    self.output_batch[acc_id] = []
        
        # Отметка завершенных процессов
        for acc_id, proc_data in list(self.processes.items()):
            process = proc_data.get('process')
            if process and process.poll() is not None and not proc_data.get('_gui_marked_stopped_'):
                return_code = process.returncode
                self.mark_bot_stopped_in_gui(acc_id, f"завершен с кодом {return_code}")
        
        # Планируем следующую проверку очереди вывода
        self.root.after(self.gui_update_interval, self.process_global_output_queue)

    def update_text_area(self, acc_id, text):
        """Добавляет текст в ScrolledText с ограничением размера лога."""
        if acc_id in self.processes:
            proc_data = self.processes[acc_id]
            text_area = proc_data['text_area']
            
            # Включаем редактирование
            text_area.config(state=tk.NORMAL)
            
            # Добавляем новый текст
            text_area.insert(tk.END, text)
            
            # Ограничиваем размер лога
            line_count = int(text_area.index('end-1c').split('.')[0])
            if line_count > self.max_log_lines:
                # Удаляем старые строки
                lines_to_delete = line_count - self.max_log_lines
                text_area.delete(1.0, f"{lines_to_delete + 1}.0")
                # Добавляем маркер об усечении
                text_area.insert(1.0, f"--- Лог обрезан (удалено {lines_to_delete} строк) ---\n")
            
            # Автопрокрутка только если пользователь в конце лога
            text_area.see(tk.END)
            
            # Блокируем редактирование
            text_area.config(state=tk.DISABLED)

            # Обновляем время последнего вывода
            current_time = time.time()
            proc_data['last_output_time'] = current_time

            # Если бот был помечен как зависший, но пришел вывод, снимаем отметку
            if proc_data.get('_gui_marked_stuck_'):
                if proc_data['process'] and proc_data['process'].poll() is None:
                    print(f"GUI: Бот {acc_id} снова активен (получен вывод).")
                    self._update_bot_list_item_display(acc_id, "▶")
                    proc_data['_gui_marked_stuck_'] = False

    def stop_bot(self, acc_id):
        """Останавливает процесс бота."""
        print(f"Остановка бота: {acc_id}")
        if acc_id in self.processes:
            proc_data = self.processes[acc_id]
            process = proc_data['process']
            if process.poll() is None: # Если процесс еще жив
                try:
                    process.terminate() # Посылаем сигнал SIGTERM
                    process.wait(timeout=5) # Ждем завершения (с таймаутом)
                    print(f"Процесс {acc_id} остановлен через terminate.")
                except subprocess.TimeoutExpired:
                    print(f"Процесс {acc_id} не остановился за 5 секунд, используем kill.")
                    process.kill() # Если не остановился - SIGKILL
                    process.wait() # Ждем завершения после kill
                    print(f"Процесс {acc_id} остановлен через kill.")
                except Exception as e:
                    print(f"Ошибка при остановке процесса {acc_id}: {e}")
                    tk.messagebox.showerror("Ошибка остановки", f"Не удалось остановить бота {acc_id}: {e}")

            self.update_text_area(acc_id, f"--- Процесс {acc_id} остановлен вручную ---\n")
            self.disable_controls(acc_id) # Деактивируем кнопки "Стоп"
            # Не удаляем процесс из словаря, чтобы можно было перезапустить
            self.mark_bot_stopped_in_gui(acc_id, "остановлен вручную")

    def disable_controls(self, acc_id):
         """Деактивирует кнопки управления для завершенного/остановленного бота."""
         if acc_id in self.processes:
             proc_data = self.processes[acc_id]
             if 'stop_button' in proc_data and proc_data['stop_button']:
                try:
                    proc_data['stop_button'].config(state=tk.DISABLED)
                except tk.TclError:
                    pass # Кнопка могла быть уже удалена/уничтожена
             # Кнопку перезапуска оставляем активной
             # Кнопку "Закрыть" теперь активируем в mark_bot_stopped_in_gui

    def restart_bot(self, acc_id):
        """Перезапускает процесс бота."""
        print(f"Перезапуск бота: {acc_id}")
        if acc_id in self.processes:
            # Сначала останавливаем старый процесс, если он еще работает
            old_proc_data = self.processes[acc_id]
            if old_proc_data['process'].poll() is None:
                print(f"Процесс {acc_id} еще работает, останавливаем перед перезапуском...")
                self.stop_bot(acc_id) # Это вызовет mark_bot_stopped_in_gui

            # Удаляем старые виджеты и данные
            # self.close_bot_tab(acc_id, is_restarting=True) # close_bot_tab удалит из списка и т.д.
            
            # Имитируем закрытие вкладки, чтобы очистить ресурсы и список
            self.remove_bot_view_and_data(acc_id, called_from_restart=True)

            # Запускаем бота заново
            print(f"Запуск нового экземпляра бота {acc_id} (после перезапуска)...")
            self.start_bot(acc_id) # Создаст новую запись в списке и UI, и выберет его
        else:
            print(f"Бот {acc_id} не найден для перезапуска.")
            tk.messagebox.showinfo("Информация", f"Бот {acc_id} не был запущен ранее.")

    def close_bot_tab(self, acc_id): # Renamed from close_bot_tab for clarity
        """Закрывает представление бота и очищает его данные."""
        print(f"Запрос на закрытие бота: {acc_id}")
        self.remove_bot_view_and_data(acc_id)

    def remove_bot_view_and_data(self, acc_id, called_from_restart=False):
        proc_data = self.processes.get(acc_id)

        if not proc_data:
            print(f"Данные для бота {acc_id} не найдены. Возможно, уже удален.")
            return

        process_obj = proc_data.get('process')
        # При перезапуске процесс уже должен быть остановлен stop_bot в restart_bot
        if not called_from_restart and process_obj and process_obj.poll() is None:
            tk.messagebox.showwarning("Бот еще работает", f"Бот {acc_id} все еще работает. Остановите его перед закрытием.")
            return
        
        # Скрываем и уничтожаем виджеты из правой панели, если они принадлежат этому боту
        if self.selected_acc_id == acc_id:
            if proc_data['text_area'].winfo_exists(): proc_data['text_area'].pack_forget()
            if proc_data['button_frame'].winfo_exists(): proc_data['button_frame'].pack_forget()
            self.selected_acc_id = None # Сбрасываем выбор

        if proc_data['text_area'].winfo_exists(): proc_data['text_area'].destroy()
        if proc_data['button_frame'].winfo_exists(): proc_data['button_frame'].destroy()

        # Удаляем из Listbox и active_acc_ids_in_list_order
        if acc_id in self.active_acc_ids_in_list_order:
            try:
                idx = self.active_acc_ids_in_list_order.index(acc_id)
                self.bot_listbox.delete(idx)
                self.active_acc_ids_in_list_order.pop(idx)
            except ValueError:
                 print(f"GUI: {acc_id} не найден в active_acc_ids_in_list_order при удалении.")
            except tk.TclError as e:
                 print(f"GUI: TclError при удалении {acc_id} из Listbox: {e}")
        
        # Очищаем данные из словарей
        if acc_id in self.processes:
            del self.processes[acc_id]
        # if acc_id in self.output_queues:
        #     while not self.output_queues[acc_id].empty():
        #         try: self.output_queues[acc_id].get_nowait()
        #         except queue.Empty: break
        #     del self.output_queues[acc_id]
        
        print(f"Данные для бота {acc_id} очищены.")

        # Если после закрытия текущего выбранного бота список не пуст, выбираем первый элемент
        if not called_from_restart and not self.selected_acc_id and self.bot_listbox.size() > 0:
            self.bot_listbox.select_set(0)
            self.on_bot_select()
        elif not called_from_restart and self.bot_listbox.size() == 0: # Если список пуст
            # Убедимся, что правая панель пуста (на всякий случай)
            for widget in self.console_pane_frame.winfo_children():
                widget.pack_forget()

    def mark_bot_stopped_in_gui(self, acc_id, reason_suffix=""):
        """Централизованно обновляет GUI при остановке/сбое бота."""
        proc_data = self.processes.get(acc_id)
        if not proc_data or proc_data.get('_gui_marked_stopped_'):
            return

        print(f"GUI: Отметка бота {acc_id} как остановленного. Причина: {reason_suffix}")
        self.update_text_area(acc_id, f"--- Процесс {acc_id} {reason_suffix} ---\n")
        self.disable_controls(acc_id)
        # Обновляем отображение в списке
        self._update_bot_list_item_display(acc_id, "■")
        proc_data['_gui_marked_stopped_'] = True
        proc_data['_gui_marked_stuck_'] = False # Остановленный бот не может быть зависшим

        # Активируем кнопку "Закрыть"
        if 'close_button' in proc_data and proc_data['close_button']:
             try:
                 proc_data['close_button'].config(state=tk.NORMAL)
             except tk.TclError:
                 print(f"GUI: TclError при активации кнопки 'Закрыть' для {acc_id}")

    def check_stuck_processes(self):
        """Периодически проверяет, не зависли ли боты (нет вывода)."""
        stuck_timeout = 185 # 2 минуты
        now = time.time()
        for acc_id, data in list(self.processes.items()): # Итерация по копии
            process = data.get('process')
            # Проверяем только работающие процессы, которые еще не помечены как остановленные
            if process and process.poll() is None and not data.get('_gui_marked_stopped_'):
                last_output = data.get('last_output_time')
                is_potentially_stuck = (last_output is not None) and (now - last_output > stuck_timeout)
                was_marked_stuck = data.get('_gui_marked_stuck_', False)

                if is_potentially_stuck and not was_marked_stuck:
                    # Помечаем как возможно зависший
                    print(f"GUI: Бот {acc_id} возможно завис (нет вывода > {stuck_timeout} сек).", file=sys.stderr) # Вывод в stderr для заметности
                    self.update_text_area(acc_id, f"--- ВНИМАНИЕ: Нет вывода более {stuck_timeout} секунд ---\n")
                    # self._update_tab_text(acc_id, "⚠️") # Warning symbol U+26A0 - старый вызов
                    self._update_bot_list_item_display(acc_id, "⚠️")
                    data['_gui_marked_stuck_'] = True

                    # === Добавлено: Отправка уведомления в Slack ===
                    try:
                        slack_message = f"⚠️ Бот {acc_id} возможно завис (нет вывода > {stuck_timeout} сек). Проверьте его состояние."
                        send_slack_error(slack_message, customer_id=acc_id)
                        print(f"GUI: Уведомление о возможном зависании бота {acc_id} отправлено в Slack.")
                    except Exception as e_slack:
                        print(f"GUI: Не удалось отправить уведомление о зависании бота {acc_id} в Slack: {e_slack}", file=sys.stderr)
                    # === Конец добавления ===

                # Снятие отметки "завис" происходит в update_text_area при получении нового вывода

        # Планируем следующую проверку через 15 секунд
        self.root.after(15000, self.check_stuck_processes)

    def fetch_and_display_order_details(self, acc_id_gui, order_id_data, text_area_widget):
        """Получает детали заказа и отображает их в текстовой области GUI."""
        try:
            logger_for_details = logging.getLogger(f"gui_order_details_for_{order_id_data}")
            if not logger_for_details.handlers:
                logger_for_details.addHandler(logging.NullHandler())
            
            order_info = main.get_order_details(order_id_data, logger=logger_for_details)
            if order_info:
                appt_detail = order_info.get('dashboardPageLoadData', {}).get('appointmentDetail', {})
                appt_date = appt_detail.get('appointmentDate')
                has_appt = bool(appt_date and appt_date != '0001-01-01')
                info_text = "=== Информация о заявке ===\n"
                info_text += f"Наличие записи: {'Есть' if has_appt else 'Нет'}\n"
                for key, val in order_info.items():
                    if key in ('applicants', 'data'): # Пропускаем большие данные
                        continue
                    info_text += f"{key}: {val}\n"
                info_text += "==========================\n"
                
                # Обновляем текстовую область из главного потока Tkinter
                self.root.after(0, lambda ta=text_area_widget, txt=info_text: self._insert_text_gui_thread_safe(ta, txt))
            else:
                self.root.after(0, lambda ta=text_area_widget: self._insert_text_gui_thread_safe(ta, f"Информация о заявке {order_id_data} не найдена или пуста.\n"))

        except Exception as e:
            error_msg = f"Не удалось получить информацию о заявке {order_id_data}: {e}\n"
            self.root.after(0, lambda ta=text_area_widget, msg=error_msg: self._insert_text_gui_thread_safe(ta, msg))

    def _insert_text_gui_thread_safe(self, text_area_widget, text_to_insert):
        """Безопасно вставляет текст в ScrolledText из любого потока."""
        if text_area_widget.winfo_exists():
            text_area_widget.config(state=tk.NORMAL)
            text_area_widget.insert(tk.END, text_to_insert)
            text_area_widget.config(state=tk.DISABLED)
            text_area_widget.see(tk.END)

    def on_closing(self):
        """Обработка закрытия приложения"""
        # Останавливаем все активные процессы
        for acc_id, proc_data in list(self.processes.items()):
            process = proc_data.get('process')
            if process and process.poll() is None:
                try:
                    print(f"Завершение процесса {acc_id}...")
                    process.terminate()
                    process.wait(timeout=3)
                except subprocess.TimeoutExpired:
                    print(f"Принудительное завершение процесса {acc_id}")
                    process.kill()
                    process.wait()
                except Exception as e:
                    print(f"Ошибка при завершении процесса {acc_id}: {e}")

        # Очищаем батчи и очереди
        self.output_batch.clear()
        while not self.global_output_queue.empty():
            try:
                self.global_output_queue.get_nowait()
            except queue.Empty:
                break

        print("Приложение закрыто, все процессы завершены")
        self.root.destroy()

    def open_batch_window(self):
        """Batch-функции временно недоступны."""
        messagebox.showinfo("Batch операции", "Функция массового запуска временно недоступна.")

    def stop_all_bots(self):
        """Массовая остановка всех ботов."""
        if not self.processes:
            messagebox.showinfo("Информация", "Нет ботов для остановки.")
            return
        if not messagebox.askyesno("Подтверждение", "Остановить все ботов?"):
            return
        for acc_id in list(self.processes.keys()):
            self.stop_bot(acc_id)

    def create_interface(self):
        """Создает пользовательский интерфейс"""
        # Фрейм для настроек производительности
        performance_frame = tk.Frame(self.root, relief=tk.RIDGE, bd=1)
        performance_frame.pack(fill=tk.X, padx=5, pady=2)
        
        tk.Label(performance_frame, text="Настройки производительности:", font=("Arial", 9, "bold")).pack(side=tk.LEFT, padx=5)
        
        # Чекбокс для отключения вывода неактивных ботов
        self.inactive_output_var = tk.BooleanVar(value=self.show_output_for_inactive)
        inactive_check = tk.Checkbutton(performance_frame, text="Показывать вывод неактивных ботов", 
                                      variable=self.inactive_output_var, 
                                      command=self.toggle_inactive_output)
        inactive_check.pack(side=tk.LEFT, padx=5)
        
        # Кнопка очистки всех логов
        clear_logs_btn = tk.Button(performance_frame, text="Очистить все логи", 
                                 command=self.clear_all_logs, bg="orange")
        clear_logs_btn.pack(side=tk.LEFT, padx=5)
        
        # Кнопка оптимизации для многих ботов
        optimize_btn = tk.Button(performance_frame, text="Оптимизация для 60+ ботов", 
                               command=self.optimize_for_many_bots, bg="red", fg="white")
        optimize_btn.pack(side=tk.LEFT, padx=5)
        
        # Метка с текущими настройками
        self.perf_label = tk.Label(performance_frame, text=f"Макс. строк лога: {self.max_log_lines} | Интервал: {self.gui_update_interval}мс")
        self.perf_label.pack(side=tk.RIGHT, padx=5)

        # Фрейм для ввода ID и кнопки запуска
        input_frame = tk.Frame(self.root)
        input_frame.pack(fill=tk.X, padx=5, pady=5)

        tk.Label(input_frame, text="ID аккаунта:").pack(side=tk.LEFT)
        self.acc_id_entry = tk.Entry(input_frame, width=20)
        self.acc_id_entry.pack(side=tk.LEFT, padx=5)
        self.acc_id_entry.bind('<Return>', self.start_bot_event)

        self.start_button = tk.Button(input_frame, text="Запустить бота", command=self.start_bot_event, bg="lightgreen")
        self.start_button.pack(side=tk.LEFT, padx=5)

        # Кнопка для batch операций
        self.batch_button = tk.Button(input_frame, text="Batch операции", command=self.open_batch_window, bg="lightblue")
        self.batch_button.pack(side=tk.LEFT, padx=5)

        # Кнопка остановки всех ботов
        self.stop_all_button = tk.Button(input_frame, text="Остановить всех", command=self.stop_all_bots, bg="lightcoral")
        self.stop_all_button.pack(side=tk.LEFT, padx=5)

        # Кнопка закрытия остановленных ботов
        self.close_stopped_button = tk.Button(input_frame, text="Закрыть остановленные", command=self.close_stopped_bots, bg="orange", fg="white")
        self.close_stopped_button.pack(side=tk.LEFT, padx=5)
        
        # Добавляем подсказку для кнопки
        try:
            import tkinter.ttk as ttk
            # Создаем простую подсказку
            def show_tooltip(event):
                tooltip = tk.Toplevel()
                tooltip.wm_overrideredirect(True)
                tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
                label = tk.Label(tooltip, text="Закрывает всех остановленных ботов из списка", background="lightyellow", relief=tk.SOLID, borderwidth=1)
                label.pack()
                tooltip.after(3000, tooltip.destroy)  # Закрыть через 3 секунды
            
            def hide_tooltip(event):
                pass  # Подсказка сама закроется через 3 секунды
            
            self.close_stopped_button.bind("<Enter>", show_tooltip)
        except Exception:
            pass  # Если не удалось создать подсказку, продолжаем без неё

        # Главная панель с разделением
        main_paned_window = tk.PanedWindow(self.root, orient=tk.HORIZONTAL, sashwidth=3)
        main_paned_window.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Левая панель со списком ботов
        left_frame = tk.Frame(main_paned_window, relief=tk.RIDGE, bd=1)
        main_paned_window.add(left_frame, width=300)

        tk.Label(left_frame, text="Запущенные боты:", font=("Arial", 12, "bold")).pack(pady=5)

        # Listbox для отображения всех ботов
        listbox_frame = tk.Frame(left_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.bot_listbox = tk.Listbox(listbox_frame, selectmode=tk.SINGLE)
        self.bot_listbox.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        self.bot_listbox.bind('<<ListboxSelect>>', self.on_bot_select)

        # Создаем контекстное меню для listbox
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="Закрыть бота", command=self.close_selected_bot_from_menu)
        
        # Привязываем контекстное меню к правой кнопке мыши
        self.bot_listbox.bind("<Button-3>", self.show_context_menu)  # Windows/Linux
        self.bot_listbox.bind("<Button-2>", self.show_context_menu)  # macOS

        # Scrollbar для Listbox
        listbox_scrollbar = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.bot_listbox.yview)
        listbox_scrollbar.pack(fill=tk.Y, side=tk.RIGHT)
        self.bot_listbox.config(yscrollcommand=listbox_scrollbar.set)

        # Правая панель для консоли
        self.console_pane_frame = tk.Frame(main_paned_window, relief=tk.RIDGE, bd=1)
        main_paned_window.add(self.console_pane_frame, width=600)

        # Статистика внизу
        stats_frame = tk.Frame(self.root, relief=tk.RIDGE, bd=1)
        stats_frame.pack(fill=tk.X, padx=5, pady=2)

        self.stats_label = tk.Label(stats_frame, text="Активных ботов: 0")
        self.stats_label.pack(side=tk.LEFT, padx=5)

        self.ram_usage_label = tk.Label(stats_frame, text="RAM: N/A")
        self.ram_usage_label.pack(side=tk.LEFT, padx=5)

    def toggle_inactive_output(self):
        """Переключает показ вывода для неактивных ботов"""
        self.show_output_for_inactive = self.inactive_output_var.get()
        print(f"Показ вывода неактивных ботов: {'включен' if self.show_output_for_inactive else 'отключен'}")

    def clear_all_logs(self):
        """Очищает логи всех ботов"""
        if messagebox.askyesno("Подтверждение", "Очистить логи всех ботов?"):
            for acc_id, proc_data in self.processes.items():
                text_area = proc_data.get('text_area')
                if text_area:
                    text_area.config(state=tk.NORMAL)
                    text_area.delete(1.0, tk.END)
                    text_area.config(state=tk.DISABLED)
            print("Все логи очищены")

    def update_performance_settings(self, max_lines=None, update_interval=None, throttle_interval=None):
        """Динамически обновляет настройки производительности"""
        if max_lines is not None:
            self.max_log_lines = max_lines
        if update_interval is not None:
            self.gui_update_interval = update_interval
        if throttle_interval is not None:
            self.gui_throttle_interval = throttle_interval
            
        # Обновляем отображение настроек
        self.perf_label.config(text=f"Макс. строк лога: {self.max_log_lines} | Интервал: {self.gui_update_interval}мс")
        print(f"Настройки производительности обновлены: лог={self.max_log_lines}, интервал={self.gui_update_interval}мс")

    def optimize_for_many_bots(self):
        """Применяет оптимальные настройки для большого количества ботов"""
        self.update_performance_settings(
            max_lines=500,  # Уменьшаем размер логов
            update_interval=500,  # Увеличиваем интервал обновления
            throttle_interval=0.2  # Увеличиваем throttling
        )
        # Отключаем показ вывода неактивных ботов
        self.inactive_output_var.set(False)
        self.toggle_inactive_output()
        print("Применены настройки для большого количества ботов")

    def close_stopped_bots(self):
        """Закрывает все остановленные боты"""
        if not self.processes:
            messagebox.showinfo("Информация", "Нет ботов для закрытия.")
            return
            
        # Находим остановленные боты
        stopped_bots = []
        for acc_id, proc_data in self.processes.items():
            process = proc_data.get('process')
            # Бот считается остановленным если процесс завершен или помечен как остановленный в GUI
            if (process is None or process.poll() is not None) or proc_data.get('_gui_marked_stopped_'):
                stopped_bots.append(acc_id)
        
        if not stopped_bots:
            messagebox.showinfo("Информация", "Нет остановленных ботов для закрытия.")
            return
            
        if not messagebox.askyesno("Подтверждение", f"Закрыть {len(stopped_bots)} остановленных ботов?"):
            return
            
        print(f"Закрываем {len(stopped_bots)} остановленных ботов: {stopped_bots}")
        for acc_id in stopped_bots:
            self.close_bot_tab(acc_id)
            
        # Обновляем интерфейс после закрытия ботов
        if stopped_bots:
            print(f"✅ Успешно закрыто {len(stopped_bots)} остановленных ботов")
            # Если текущий выбранный бот был закрыт, выбираем первый доступный
            if self.selected_acc_id not in self.processes and self.bot_listbox.size() > 0:
                self.bot_listbox.select_set(0)
                self.on_bot_select()

    def close_selected_bot_from_menu(self):
        """Закрывает выбранный бот из контекстного меню"""
        selection = self.bot_listbox.curselection()
        if not selection:
            return

        selected_index = selection[0]
        selected_acc_id = self.active_acc_ids_in_list_order[selected_index]
        self.close_bot_tab(selected_acc_id)

    def show_context_menu(self, event):
        """Показывает контекстное меню при нажатии правой кнопки мыши"""
        # Сначала выбираем элемент под курсором
        index = self.bot_listbox.nearest(event.y)
        if index >= 0 and index < len(self.active_acc_ids_in_list_order):
            self.bot_listbox.selection_clear(0, tk.END)
            self.bot_listbox.selection_set(index)
            self.bot_listbox.activate(index)
            
            # Получаем ID выбранного бота
            selected_acc_id = self.active_acc_ids_in_list_order[index]
            proc_data = self.processes.get(selected_acc_id)
            
            if proc_data:
                # Очищаем старое меню
                self.context_menu.delete(0, tk.END)
                
                # Определяем состояние бота
                process = proc_data.get('process')
                is_stopped = (process is None or process.poll() is not None) or proc_data.get('_gui_marked_stopped_')
                is_running = process and process.poll() is None and not proc_data.get('_gui_marked_stopped_')
                
                # Добавляем соответствующие пункты меню
                if is_stopped:
                    self.context_menu.add_command(label="Закрыть остановленный бот", 
                                                command=self.close_selected_bot_from_menu)
                    self.context_menu.add_command(label="Перезапустить бота", 
                                                command=self.restart_selected_bot_from_menu)
                elif is_running:
                    self.context_menu.add_command(label="Остановить бота", 
                                                command=self.stop_selected_bot_from_menu)
                    self.context_menu.add_command(label="Перезапустить бота", 
                                                command=self.restart_selected_bot_from_menu)
                
                # Показываем меню
                self.context_menu.post(event.x_root, event.y_root)

    def stop_selected_bot_from_menu(self):
        """Останавливает выбранный бот из контекстного меню"""
        selection = self.bot_listbox.curselection()
        if not selection:
            return

        selected_index = selection[0]
        selected_acc_id = self.active_acc_ids_in_list_order[selected_index]
        self.stop_bot(selected_acc_id)

    def restart_selected_bot_from_menu(self):
        """Перезапускает выбранный бот из контекстного меню"""
        selection = self.bot_listbox.curselection()
        if not selection:
            return

        selected_index = selection[0]
        selected_acc_id = self.active_acc_ids_in_list_order[selected_index]
        self.restart_bot(selected_acc_id)

if __name__ == "__main__":
    root = tk.Tk()
    app = BotManagerApp(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing) # Обработка закрытия окна
    root.mainloop() 