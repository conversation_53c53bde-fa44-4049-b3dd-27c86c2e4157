import datetime

# Правила поиска дат (используются как fallback если данные с сервера недоступны)
# Основные правила дат теперь приходят с сервера в recordDates
DATE_RULES = {
    'start_date': datetime.date(2025, 5, 1),  # первая допустимая дата (fallback)
    'end_date':   datetime.date(2025, 7, 15), # последняя допустимая дата (fallback)
    'min_days':   0,                          # минимальный отступ от сегодня (fallback)
    'exclude_dates': []                       # исключаемые даты (fallback)
}

# Формат данных с сервера (recordDates):
# {
#     "endDate": "2025-07-24",
#     "startDate": "2025-07-03", 
#     "daysBefore": "1",
#     "excludeDates": ["2025-07-13", "2025-07-14", ...]
# }

# URL для Slack уведомлений
SLACK_WEBHOOK_URL = "*********************************************************************************"

# URLs для VFS API
DASHBOARD_URL = "https://atlantis-absapi-ru.vfsglobal.com/v1/applications/dashboard"
AUTH_URL = "https://keycloak.vfsglobal.com/realms/vfs_realm/protocol/openid-connect/token"
NAVIGATION_URL = "https://atlantis-absapi-ru.vfsglobal.com/v1/applications/navigation"
CAPPING_URL = "https://atlantis-absapi-ru.vfsglobal.com/v1/applications/capping/serviceLevel"
SLOTS_URL = "https://atlantis-absapi-ru.vfsglobal.com/v1/applications/abs/absdetails"
BOOKING_URL = "https://atlantis-absapi-ru.vfsglobal.com/v1/applications/payment/process"

# Таймауты и интервалы
REQUEST_TIMEOUT = 180
SLOT_CHECK_INTERVAL = 15
ERROR_RETRY_DELAY = 30
CRITICAL_ERROR_DELAY = 60

# VFS Auth данные
VFS_CLIENT_SECRET = "YN4f3a1IAmKVTc4auZKFhZaNcQhbTL2m"
VFS_CLIENT_ID = "test-cors"

# Headers
DEFAULT_HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
    "Accept": "application/json, text/plain, */*",
    "Origin": "https://atlantis-abs-uk-ru.vfsglobal.com",
    "Referer": "https://atlantis-abs-uk-ru.vfsglobal.com/"
} 