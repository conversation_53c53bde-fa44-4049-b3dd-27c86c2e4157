import json
import datetime
import logging
import time
import random
import os

from curl_cffi import requests
from EncryptionModule import encrypt_request_body
from .config import (
    DATE_RULES, SLACK_WEBHOOK_URL, DASHBOARD_URL, AUTH_URL, 
    NAVIGATION_URL, CAPPING_URL, SLOTS_URL, BOOKING_URL,
    REQUEST_TIMEOUT, SLOT_CHECK_INTERVAL, ERROR_RETRY_DELAY,
    VFS_CLIENT_SECRET, VFS_CLIENT_ID, DEFAULT_HEADERS
)
from functions.timing_utils import SimpleTimer, test_server_latency

# Новое исключение для ошибки 429 при бронировании
class RetryBookingAfter429Exception(Exception):
    """Исключение для сигнализации о необходимости повторить бронирование после 429"""
    pass

class BookingSuccessException(Exception):
    """Исключение для остановки при успешном бронировании"""
    pass

class ClientBooking():
    def __init__(self, account):
        self.session = requests.Session(impersonate='firefox135',http_version=3)
        self.phone = account['phone']
        self.vacCode = account['visaCenter'][:-4]
        self.vacId = int(account['visaCenter'][4:])
        self.proxy = account.get('proxy')
        self.monitoring_only = account.get('monitoringOnly', False)
        
        if self.proxy:
            self.session.proxies = {'http': f'http://{self.proxy}', 'https': f'http://{self.proxy}'}
        
        self.vfs_auth_data = json.loads(account['applicants'])[0]
        
        # Парсим даты с сервера
        self.customer_dates = json.loads(account['recordDates'])
        self.gwf_number = self.vfs_auth_data['gwf_number']
        
        # Преобразуем даты в правильный формат для работы
        self.date_rules = self._parse_customer_dates(self.customer_dates)
        
        # Получаем токен и навигационные данные
        self.token = self.get_access_token()
        self.navigation_data = self.get_navigation_data()
        
        # Извлекаем важные данные из навигации
        app_data = self.navigation_data['LandingPageData'][0]['AppData']
        self.VisaSubType = app_data['VisaSubType']
        self.mission_code = app_data['MissionCode']
        self.country_code = app_data['CountryCode']
        self.user_name = app_data['GivenName'] + ' ' + app_data['LastName']
        self.ApplicantId = app_data['ApplicantId']
        self.missionId = app_data['MissionId']
        self.countryId = app_data['CountryId']
        
        # Получаем данные о capping
        self.capping_level = self.get_capping()
        
        # Логируем используемые правила дат
        logging.info(f"Используемые правила дат: {self.date_rules}")

        # Для обработки ошибки 429 при бронировании
        self.current_429_booking_retries = 0
        self.max_429_booking_retries = 3 # Максимальное количество последовательных попыток после 429

    def _parse_customer_dates(self, customer_dates):
        """Преобразует даты с сервера в формат для внутренней работы"""
        try:
            start_date = datetime.datetime.strptime(customer_dates['startDate'], '%Y-%m-%d').date()
            end_date = datetime.datetime.strptime(customer_dates['endDate'], '%Y-%m-%d').date()
            min_days = int(customer_dates.get('daysBefore', 0))
            
            # Преобразуем строки дат в объекты date
            exclude_dates = []
            for date_str in customer_dates.get('excludeDates', []):
                exclude_dates.append(datetime.datetime.strptime(date_str, '%Y-%m-%d').date())
            
            return {
                'start_date': start_date,
                'end_date': end_date,
                'min_days': min_days,
                'exclude_dates': exclude_dates
            }
        except (KeyError, ValueError) as e:
            logging.error(f"Ошибка парсинга дат с сервера: {e}. Используем fallback правила из config.")
            # Fallback на дефолтные правила из config
            return DATE_RULES

    def get_capping(self):
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
            **DEFAULT_HEADERS
        }
        payload = {
            "missionCode": self.mission_code,
            "countryCode": self.country_code,
            "vacCode": self.vacCode,
            "visaCategory": self.VisaSubType,
            "AccessToken": self.token,
            "gwfNumber": self.vfs_auth_data['gwf_number']
        }
        response = self.session.post(CAPPING_URL, json=payload, headers=headers, impersonate='firefox135', timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            logging.info("Service level capping data retrieved successfully")
            return response.json()
        else:
            raise Exception(f"Service level capping request failed: {response.status_code} - {response.text}")

    def get_access_token(self):
        AUTH_DATA = {
            "grant_type": "password",
            "client_id": VFS_CLIENT_ID, 
            "username": self.vfs_auth_data['gwf_number'],
            "password": self.vfs_auth_data['uk_email'],
            "client_secret": VFS_CLIENT_SECRET
        }
        response = self.session.post(AUTH_URL, data=AUTH_DATA, impersonate='firefox135', timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            logging.info("Token obtained successfully")
            return response.json()["access_token"]
        else:
            raise Exception(f"Authentication failed: {response.status_code} - {response.text}")

    def get_navigation_data(self):
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
            **DEFAULT_HEADERS
        }
        payload = {
            "GWFNumber": self.vfs_auth_data['gwf_number'],
            "AccessToken": self.token
        }
        response = self.session.post(NAVIGATION_URL, json=payload, headers=headers, impersonate='firefox135', timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            logging.info("Navigation data retrieved successfully")
            return response.json()
        else:
            raise Exception(f"Navigation request failed: {response.status_code} - {response.text}")

    def _create_check_payload(self):
        """Создает payload для проверки слотов с актуальным токеном"""
        return {
            "ApplicantUan": self.ApplicantId,
            "phoneNumber": self.phone.replace('+', '').replace('7', '', 1),
            "AccessToken": self.token,
            "action": "schedule",
            "appointmentServiceCode": "",
            "appointmentServiceId": 0,
            "calendarTypeId": 0,
            "countryCode": self.country_code,
            "countryId": self.countryId,
            "gwfNumber": self.gwf_number,
            "missionCode": self.mission_code,
            "missionId": self.missionId,
            "requestDate": "",
            "serviceLevel": self.capping_level['serviceLevels'][0]['serviceLevelCode'],
            "serviceLevelId": self.capping_level['serviceLevels'][0]['serviceLevelId'],
            "userName": self.user_name,
            "vacCode": self.vacCode,
            "vacId": self.vacId,
            "visaCategoryId": self.capping_level['visaCategories']['visaCategoryId'],
            "visaSubtype": self.capping_level['visaCategories']['visaCategoryCode']
        }

    def check_slot_availability(self):
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
            **DEFAULT_HEADERS
        }
        
        # Создаем payload с актуальным токеном
        check_payload = self._create_check_payload()
        
        # Debug логирование для сравнения с grok_via_json.py
        logging.debug(f"Check payload: {json.dumps(check_payload, ensure_ascii=False, indent=2)}")
        
        current_retry_delay_429 = 15  # Начальная задержка для 429 при сканировании
        max_retry_delay_429 = 60      # Максимальная задержка для 429 при сканировании
        
        while True:
            response = self.session.post(SLOTS_URL, json=check_payload, headers=headers, impersonate='firefox135', timeout=REQUEST_TIMEOUT)
            
            if response.status_code == 429:
                logging.warning(f"Получена ошибка 429 (Too Many Requests) при проверке слотов. "
                                f"Ожидание {current_retry_delay_429} секунд.")
                time.sleep(current_retry_delay_429)
                current_retry_delay_429 = min(current_retry_delay_429 + 15, max_retry_delay_429) 
                continue # Повторяем запрос
            
            # Если не 429, то при следующем вызове check_slot_availability current_retry_delay_429 снова будет 15.
            
            if response.status_code == 200:
                data = response.json()
                # data = {
                #     "absMonths": ["June 2025"],
                #     "absDates": ["2025-06-20"],
                #     "appointmentServices": [
                #         {
                #             "calendarTypeId": 2,
                #             "serviceId": 0,
                #             "serviceCode": None,
                #             "serviceName": "Standard (Assisted)",
                #             "amount": None,
                #             "currencyName": None
                #         }
                #     ],
                #     "earliestAvailableSlot": [
                #         {
                #             "calendarTypeId": 2,
                #             "appointmentServiceId": None,
                #             "visaGroupId": None,
                #             "availableDate": "6/20/2025",
                #             "slotStartTime": "9:15 AM"
                #         },
                #         {
                #             "calendarTypeId": 1,
                #             "appointmentServiceId": None,
                #             "visaGroupId": None,
                #             "availableDate": "",
                #             "slotStartTime": None
                #         },
                #         {
                #             "calendarTypeId": 3,
                #             "appointmentServiceId": None,
                #             "visaGroupId": None,
                #             "availableDate": "",
                #             "slotStartTime": None
                #         }
                #     ],
                #     "availableSlot": [
                #         {
                #             "calendarTypeId": 2,
                #             "appointmentServiceId": None,
                #             "sequenceNo": 1,
                #             "actualTimeBandId": 12053,
                #             "noOfSlots": 2,
                #             "slotAllocatedDate": "2025-06-20",
                #             "actualStartTime": "9:15 AM",
                #             "actualEndTime": "9:30 AM",
                #             "slotStartTime": "9:00 AM",
                #             "slotEndTime": "10:00 AM"
                #         }
                #     ],
                #     "csl5Slots": {
                #         "calendarTypeId": 2,
                #         "availableDate": "2025-06-20T00:00:00",
                #         "slotStartTime": "2025-06-20T09:15:00"
                #     },
                #     "error": None
                # }
                if data.get("code") == 5002:
                    logging.info("No slots available")
                    return False
                else:
                    msg_log = f"Slots found: {json.dumps(data, ensure_ascii=False)}"
                    logging.info(msg_log)
                    return data # Успешный выход из цикла
            else:
                msg = f"Slot check failed: {response.status_code} - {response.text}"
                logging.error(msg)
                return False # Ошибка, выход из цикла

    def get_dashboard_data(self):
        """Проверяет существующие записи через dashboard endpoint"""
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
            **DEFAULT_HEADERS
        }
        payload = {
            "missionCode": self.mission_code,
            "countryCode": self.country_code,
            "vacCode": self.vacCode,
            "gwfNumber": self.gwf_number,
            "AccessToken": self.token
        }
        response = self.session.post(DASHBOARD_URL, json=payload, headers=headers, impersonate='firefox135', timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            return response.json()
        else:
            logging.warning(f"Dashboard request failed: {response.status_code} - {response.text}")
            return {}

    def check_existing_appointment(self):
        """Проверяет наличие существующей записи"""
        dashboard_data = self.get_dashboard_data()
        dp = dashboard_data.get("dashboardPageLoadData") or {}
        appointment = dp.get("appointmentDetail")
        appointment_date = appointment.get('appointmentDate') if appointment else None
        
        if appointment and appointment_date and appointment_date != '0001-01-01':
            customer = dp.get('customerDetail', {})
            addr = appointment.get('vacAddress', {})
            addr_str = ', '.join(filter(None, [
                addr.get('line1',''), 
                addr.get('line2',''), 
                addr.get('line3',''), 
                addr.get('townCity','')
            ]))
            date = appointment_date
            time_str = customer.get('appointmentTime', '')
            msg = f"Существующая запись: {addr_str} | {date} {time_str}"
            logging.info(msg)
            
            if not self.monitoring_only:
                raise BookingSuccessException(msg)
            return True
        return False

    def date_in_range(self, date_str):
        """Проверяет, попадает ли дата в допустимый диапазон используя данные с сервера"""
        try:
            slot_date = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
            today = datetime.date.today()
            
            # Проверяем минимальный отступ от сегодня
            if (slot_date - today).days < self.date_rules['min_days']:
                logging.debug(f"Дата {date_str} слишком близко к сегодня (менее {self.date_rules['min_days']} дней)")
                return False
                
            # Проверяем диапазон дат
            if slot_date < self.date_rules['start_date'] or slot_date > self.date_rules['end_date']:
                logging.debug(f"Дата {date_str} вне допустимого диапазона {self.date_rules['start_date']} - {self.date_rules['end_date']}")
                return False
                
            # Проверяем исключаемые даты
            if slot_date in self.date_rules['exclude_dates']:
                logging.debug(f"Дата {date_str} в списке исключаемых дат")
                return False
                
            logging.debug(f"Дата {date_str} прошла все проверки")
            return True
            
        except ValueError as e:
            logging.error(f"Ошибка парсинга даты {date_str}: {e}")
            return False

    def send_slack_notification(self, valid_slots):
        """Отправляет уведомление в Slack о найденных слотах"""
        if not SLACK_WEBHOOK_URL or not valid_slots:
            return

        message_lines = [f"🔔 Найдены слоты для GWF {self.gwf_number} в городе {self.vacCode}:"]
        
        # Добавляем информацию о правилах дат
        message_lines.append(f"📅 Период: {self.date_rules['start_date']} - {self.date_rules['end_date']}")
        if self.date_rules['exclude_dates']:
            exclude_count = len(self.date_rules['exclude_dates'])
            message_lines.append(f"❌ Исключено дат: {exclude_count}")
        
        # Добавляем найденные слоты
        for slot in valid_slots:
            date = slot.get('slotAllocatedDate')
            start_time = slot.get('actualStartTime', '?')
            message_lines.append(f"  ✅ {date} {start_time}")

        payload = {"text": "\n".join(message_lines)}
        try:
            response = self.session.post(SLACK_WEBHOOK_URL, json=payload, timeout=10)
            if response.status_code == 200:
                logging.info("Slack-оповещение успешно отправлено")
            else:
                logging.error(f"Ошибка отправки Slack-оповещения: {response.status_code} - {response.text}")
        except Exception as e:
            logging.exception("Исключение при отправке Slack-оповещения")

    def build_booking_payload(self, slot):
        """Формирует payload для бронирования слота"""
        # Получаем актуальный check_payload
        check_payload = self._create_check_payload()
        
        dob_str = datetime.datetime.utcnow().isoformat(timespec='milliseconds') + 'Z'
        slot_payload = {
            'appointmentServiceId': 0,
            'appointmentServiceName': 'Standard Assisted',
            'dateOfBooking': dob_str,
            'actualTimeBandId': slot['actualTimeBandId'],
            'appointmentDate': slot['slotAllocatedDate'],
            'startTime': slot['actualStartTime'],
            'endTime': slot['actualEndTime'],
            'appointmentStateId': 1,
            'calenderTypeId': slot['calendarTypeId'],
            'isConfirmed': 0,
            'action': 'schedule',
            'serviceLevelId': check_payload['serviceLevelId'],
        }
        
        payload = {
            'loggedInUser': self.user_name,
            'IsForBuyAdditionalService': False,
            'IsForPaymentRetry': False,
            'PaymentMode': 1,
            'missionCode': self.mission_code,
            'countryCode': self.country_code,
            'vacCode': self.vacCode,
            'missionId': str(self.missionId),
            'countryId': self.countryId,
            'vacId': self.vacId,
            'applicantUan': self.ApplicantId,
            'applicantId': 1,
            'AccessToken': self.token,
            'phoneNumber': check_payload['phoneNumber'],
            'shippingPhoneNumber': '',
            'visaCategoryId': check_payload['visaCategoryId'],
            'visaCategoryCode': check_payload.get('visaSubtype'),
            'noShow': False,
            'applicantGroupUAN': self.gwf_number,
            'isCancelledReBooking': False,
            'isApptConfirmed': False,
            'isReschedule': False,
            'myProperty': 0,
            'languageCode': 'en-US',
            'applicantVac': {
                'vacCode': self.vacCode,
                'fees': None,
                'penaltyFee': None,
                'supportIds': []
            },
            'applicantServiceLevel': {
                'serviceLevel': check_payload['serviceLevelId'],
                'ServiceLevelCode': check_payload['serviceLevel'],
                'fees': None
            },
            'slot': [slot_payload],
            'avs': {
                'avsGroup': [
                    {
                        'id': 1,
                        'isMandatory': False,
                        'avsService': [
                            {
                                'serviceId': 6,
                                'serviceName': 'Document Self Upload',
                                'serviceCode': 'SCANUPLOAD',
                                'serviceType': 'AVS',
                                'isCourierService': 0,
                                'unitPrice': '0.00',
                                'unit': 1,
                                'currencyCode': 'RUB',
                                'freeService': 1,
                                'isPackage': 0,
                                'avsTax': [
                                    {'taxPercentage': '0.00', 'taxTypeCode': 'CGST'},
                                    {'taxPercentage': '0.00', 'taxTypeCode': 'SGST'}
                                ],
                                'includedAvs': None
                            }
                        ]
                    }
                ],
                'RemovedServiceIDs': None
            },
            'visaReservation': None,
            'courier': {'courierType': 'Courier', 'courierPartnerId': 0, 'isHalAddress': False, 'isCourier': 0},
            'deliveryAddress': {'addressline1':'','addressline2':'','city':'','state':'','postalCode':'','country':''},
            'odmv': {'deliveryAddress': {'addressline1':'','addressline2':'','city':'','state':'','postalCode':''}, 'isODMV': False}
        }
        return payload

    def book_appointment(self, slot):
        """Пытается забронировать указанный слот"""
        booking_payload = self.build_booking_payload(slot)
        
        logging.info('Raw booking payload: %s', json.dumps(booking_payload, ensure_ascii=False))
        
        # Шифруем payload
        encrypted = encrypt_request_body(BOOKING_URL, booking_payload)
        
        headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json',
            **DEFAULT_HEADERS
        }
        
        resp = self.session.post(BOOKING_URL, json=encrypted, headers=headers, impersonate='firefox135', timeout=REQUEST_TIMEOUT)
        logging.info('Booking HTTP status %s, response: %s', resp.status_code, resp.text)

        if resp.status_code == 429:
            logging.warning("Получена ошибка 429 (Too Many Requests) при бронировании слота.")
            raise RetryBookingAfter429Exception()
        
        if resp.status_code != 200:
            logging.error('Booking error %s: %s', resp.status_code, resp.text)
            # Дополнительная проверка для кода ошибки 6057 в теле ответа при статусе 400
            if resp.status_code == 400:
                try:
                    err_data = resp.json()
                    if err_data.get('error', {}).get('code') == 6057:
                         logging.error('Слот больше недоступен (код 6057 в теле ответа 400).')
                except json.JSONDecodeError:
                    pass # Оставляем общую логику ошибки
            return None # Общая ошибка бронирования, не 429
            
        h = resp.json()
        logging.info('Booking response: %s', json.dumps(h, ensure_ascii=False))
        
        # Анализ ответа
        code = h.get('code')
        pg = h.get('pgDetails') or {}
        error_field = h.get('error')  # Может быть None, {}, или объект с ошибкой
        
        # Список кодов ошибок, которые означают неуспешное бронирование
        ERROR_CODES = [
            6057,  # Слот недоступен
            7522,  # Failure to update the slot  
            240,   # Ошибка обновления слота
            635,   # Различные ошибки бронирования
            616,   # Ошибки бронирования
            2007,  # Платёжный шлюз недоступен
            # Добавим другие известные коды ошибок при необходимости
        ]
        
        # Проверяем на наличие кода ошибки в основном поле
        if code is not None and code in ERROR_CODES:
            if code == 6057:
                logging.warning('Слот больше недоступен (ответ сервера с кодом 6057).')
            elif code == 7522:
                logging.error('Ошибка обновления слота (код 7522): Failure to update the slot')
            elif code == 2007:
                logging.warning('Платёжный шлюз недоступен (code 2007)')
            else:
                logging.error(f'Ошибка бронирования с кодом {code}')
            return None  # Ошибка бронирования
            
        # Проверяем наличие ошибки в поле error (если оно не null и не пустое)
        if error_field is not None and error_field != {}:
            error_code = error_field.get('code') if isinstance(error_field, dict) else None
            error_message = error_field.get('message', 'Неизвестная ошибка') if isinstance(error_field, dict) else str(error_field)
            logging.error(f'Ошибка бронирования в поле error: код {error_code}, сообщение: {error_message}')
            return None  # Ошибка бронирования
        
        # Успешное бронирование: error равен null/None, нет кода ошибки
        if error_field is None and (code is None or code not in ERROR_CODES):
            if pg.get('url'):
                msg = f'Перенаправление на платёжный шлюз: {pg["url"]} (успешное бронирование).'
                logging.info(msg)
                raise BookingSuccessException(msg)
            else:
                msg = 'Переход на страницу статуса без оплаты (успешное бронирование).'
                logging.info(msg)
                raise BookingSuccessException(msg)
        
        # Если мы дошли до этого места, значит что-то непонятное
        logging.error(f'Неожиданный ответ бронирования: {h}')
        return h  # Возвращаем ответ для дальнейшего анализа

    def refresh_token(self):
        """Обновляет access token"""
        try:
            self.token = self.get_access_token()
            return True
        except Exception as e:
            logging.error(f"Ошибка обновления токена: {e}")
            return False

    def start_monitoring(self):
        """Запускает основной цикл мониторинга и бронирования"""
        logging.info(f"Запуск мониторинга для GWF {self.gwf_number}")
        
        # Подробное логирование правил дат
        logging.info(f"Период бронирования: {self.date_rules['start_date']} - {self.date_rules['end_date']}")
        logging.info(f"Минимальный отступ от сегодня: {self.date_rules['min_days']} дней")
        if self.date_rules['exclude_dates']:
            logging.info(f"Исключаемые даты ({len(self.date_rules['exclude_dates'])}): {self.date_rules['exclude_dates']}")
        else:
            logging.info("Исключаемых дат нет")
        
        # Отправляем стартовое уведомление в Slack
        try:
            start_message = f"🚀 Запуск мониторинга для GWF {self.gwf_number}\n"
            start_message += f"📅 Период: {self.date_rules['start_date']} - {self.date_rules['end_date']}\n"
            start_message += f"⏰ Отступ: {self.date_rules['min_days']} дней\n"
            if self.date_rules['exclude_dates']:
                start_message += f"❌ Исключено дат: {len(self.date_rules['exclude_dates'])}"
            else:
                start_message += "✅ Исключаемых дат нет"
            
            payload = {"text": start_message}
            self.session.post(SLACK_WEBHOOK_URL, json=payload, timeout=10)
        except Exception:
            logging.exception("Ошибка отправки стартового уведомления в Slack")
        
        # Инициализация точного таймера и вычисление опережения
        logging.info("Инициализация точного таймера и NTP синхронизация")
        self.timer = SimpleTimer()
        # Формируем payload и headers для теста задержки слотов
        test_payload = self._create_check_payload()
        test_headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
            **DEFAULT_HEADERS
        }
        self.advance_seconds = test_server_latency(
            self.session,
            SLOTS_URL,
            json_payload=test_payload,
            headers=test_headers
        )
        logging.info(f"Рекомендуемое опережение для запросов: {self.advance_seconds:.3f}s")

        while True:
            try:
                #Проверяем существующие записи
                if self.check_existing_appointment():
                     if not self.monitoring_only:
                        return  # Есть запись и не режим мониторинга
                
                # Полный цикл проверки с обновлением токена
                if self.refresh_token():
                    # Обновляем level capping для нового токена
                    self.navigation_data = self.get_navigation_data()
                    self.capping_level = self.get_capping()
                else:
                    logging.error(f"Не удалось обновить токен, пропускаем цикл. Ожидание {ERROR_RETRY_DELAY} сек.")
                    time.sleep(ERROR_RETRY_DELAY)
                    continue
                
                # Основной цикл быстрых проверок
                special_attempts = 0
                for i in range(30): # Цикл быстрых проверок под одним токеном
                    now = self.timer.get_precise_datetime()
                    interval = SLOT_CHECK_INTERVAL # По умолчанию
                    
                    # Специальный режим в 21:30
                    if special_attempts == 0 and now.hour == 21 and now.minute == 29 and now.second >= 30:
                        target = now.replace(hour=21, minute=30, second=0, microsecond=0)
                        current_interval_calc = (target - now).total_seconds()
                        adjusted_calc = current_interval_calc - (self.advance_seconds / 2)
                        interval = max(0, adjusted_calc) # Убедимся, что не отрицательный
                        special_attempts = 1
                        
                        msg_pause = f"⏸ Приостановка сканирования до {target.strftime('%H:%M:%S')} (ожидание {interval:.2f}s)"
                        logging.info(msg_pause)
                        
                        try:
                            payload_slack = {"text": msg_pause}
                            self.session.post(SLACK_WEBHOOK_URL, json=payload_slack, timeout=10)
                        except Exception:
                            logging.exception("Ошибка отправки Slack-уведомления о паузе")
                    
                    elif special_attempts == 1 and now.hour == 21 and now.minute == 30: # Сразу после 21:30:00
                        interval = 0 
                        special_attempts = 2
                    elif special_attempts == 2 and now.hour == 21 and now.minute == 30: # Это будет почти сразу после предыдущего
                        interval = 5 
                        special_attempts = 3
                    # else: # Используем SLOT_CHECK_INTERVAL по умолчанию
                    
                    logging.info(f"Check {i + 1}/30, waiting {interval:.2f}s...")
                    time.sleep(interval)
                    
                    # Проверяем слоты
                    slots_data = self.check_slot_availability() # Уже обрабатывает 429 для сканирования
                    if slots_data:
                        self.process_available_slots(slots_data) # Может кинуть RetryBookingAfter429 или BookingSuccess
                        # Если process_available_slots завершился без RetryBookingAfter429Exception
                        # (и без BookingSuccessException, который бы прервал цикл),
                        # это означает, что попытки бронирования (если были) не столкнулись с 429.
                        # Поэтому сбрасываем счетчик попыток 429 для бронирования.
                        self.current_429_booking_retries = 0
                        
            except RetryBookingAfter429Exception:
                logging.warning("Получена ошибка 429 при бронировании слота.")
                self.current_429_booking_retries += 1
                if self.current_429_booking_retries >= self.max_429_booking_retries:
                    logging.error(f"Достигнут лимит ({self.max_429_booking_retries}) попыток бронирования после 429. "
                                  f"Ожидание {ERROR_RETRY_DELAY}с и сброс счетчика.")
                    self.current_429_booking_retries = 0 
                    time.sleep(ERROR_RETRY_DELAY)
                else:
                    logging.info(f"Попытка бронирования {self.current_429_booking_retries}/{self.max_429_booking_retries} после ошибки 429. "
                                 f"Ожидание 15с перед новым циклом мониторинга.")
                    time.sleep(15)
                # В обоих случаях (превышен лимит или нет), мы хотим начать новый цикл мониторинга,
                # который включает обновление токена, capping и т.д.
                continue # Продолжаем внешний while True цикл

            except BookingSuccessException as success_info:
                logging.info(f"Успешное бронирование: {success_info}")
                raise  # Пробрасываем для обработки в main.py
            except Exception as e:
                logging.error(f"Ошибка в цикле мониторинга: {e}")
                time.sleep(ERROR_RETRY_DELAY)

    def process_available_slots(self, slots_data):
        """Обрабатывает найденные слоты: уведомления и бронирование"""
        available_slots = slots_data.get('availableSlot', [])
        if not available_slots:
            return
        
        # Фильтруем слоты по датам используя правила с сервера
        valid_slots = []
        for slot in available_slots:
            slot_date = slot.get('slotAllocatedDate')
            if slot_date and self.date_in_range(slot_date):
                valid_slots.append(slot)
            elif slot_date:
                logging.debug(f"Слот на {slot_date} отфильтрован по правилам дат")
        
        if not valid_slots:
            logging.info("Нет слотов, соответствующих правилам дат с сервера")
            return
            
        logging.info(f"Найдено {len(valid_slots)} валидных слотов из {len(available_slots)}")
            
        # Отправляем Slack уведомление
        self.send_slack_notification(valid_slots)
        
        # Если режим только мониторинга, не бронируем
        if self.monitoring_only:
            return
        
        # Перемешиваем слоты и пытаемся забронировать
        random.shuffle(valid_slots)
        
        for slot_to_book in valid_slots:
            date = slot_to_book.get('slotAllocatedDate')
            logging.info(f'Найден слот на {date}, попытка бронирования')
            
            try:
                # book_appointment может возбудить RetryBookingAfter429Exception или BookingSuccessException,
                # или вернуть None/dict при других ошибках.
                booking_result = self.book_appointment(slot_to_book)
                
                # Если book_appointment вернул None (неудача, не 429, не успех)
                # или словарь с ошибкой (не 429, не успех)
                if booking_result is None or (isinstance(booking_result, dict) and booking_result.get('error')):
                    logging.warning(f'Бронирование не удалось для слота {date} (результат: {booking_result}), пробуем следующий валидный слот.')
                    continue # Пробуем следующий слот в valid_slots
                elif isinstance(booking_result, dict) and not booking_result.get('error') and booking_result.get('code') == 2007:
                    # Специальная обработка для кода 2007 (шлюз недоступен), если не было другого error
                    logging.warning(f'Платёжный шлюз недоступен (code 2007) для слота {date}, пробуем следующий валидный слот.')
                    continue # Пробуем следующий слот
                elif isinstance(booking_result, dict) and not booking_result.get('error'):
                    # Это странный случай, если book_appointment вернул dict без ошибки, но не кинул BookingSuccessException.
                    # По идее, такого быть не должно, если логика book_appointment верна.
                    # На всякий случай, если такое произошло, считаем успехом, но логируем как неожиданное.
                    logging.info(f"Бронирование для слота {date} считается успешным (неожиданный возврат словаря из book_appointment без ошибки и не BookingSuccessException).")
                    raise BookingSuccessException(f"Успешное бронирование для {date} (неожиданный возврат dict)")

            # RetryBookingAfter429Exception и BookingSuccessException пробрасываются наверх
            except RetryBookingAfter429Exception:
                raise 
            except BookingSuccessException:
                raise 
            except Exception as e: # Другие неожиданные исключения при вызове book_appointment
                logging.error(f'Неожиданная ошибка при попытке бронирования слота {date}: {e}')
                continue # Пробуем следующий слот


