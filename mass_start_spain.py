import subprocess
import sys
import time
import traceback

import requests

# URL API, который возвращает список заявок (замените на реальный URL)
api_url = 'https://new.fast-bot.ru/api/all_orders/?api_key=29140812490ksadkljals&country=Spain&status=INWORK'

# Словарь для сопоставления ID визовых центров и названий
# Основано на client/generate_stepgroup_data.py
# TODO: Рассмотреть возможность импорта или более динамического получения этого списка
# ИЗМЕНЕНО: Используем английские названия, как ожидается от API
visa_centers_mapping = {
    7: "Yekaterinburg",  # Екатеринбург
    8: "<PERSON>zhny Novgorod", # Нижний Новгород
    9: "Rostov-on-Don",   # Ростов-на-Дону
    10: "Novosibirsk",    # Новосибирск
    11: "Kazan",          # Казань
    12: "Samara",         # Самара
    14: "Moscow",         # Москва
    16: "Krasnodar",      # Краснодар
    17: "Saint Petersburg (Mobile Biometrics)" # Санкт-Петербург (выездное обслуживание) - Уточнить точное название из API
    # Добавьте другие центры при необходимости
}

# Создаем обратный маппинг (Название -> ID) для поиска ID по названию из API
# Приводим названия к нижнему регистру для надежного поиска
# Теперь ключи будут английскими: 'yekaterinburg', 'moscow' и т.д.
visa_centers_reverse_mapping = {name.lower(): city_id for city_id, name in visa_centers_mapping.items()}

# Функция для получения списка заявок через GET-запрос
def get_clients():
    retries = 5
    delay_seconds = 2
    for attempt in range(retries):
        try:
            response = requests.get(api_url)
            if response.status_code == 502:
                print(f"Ошибка API (502 Bad Gateway), попытка {attempt + 1} из {retries}...")
                if attempt < retries - 1:
                    time.sleep(delay_seconds)
                    continue 
                else:
                    # Если это последняя попытка и все еще 502
                    print(f"Не удалось получить данные с сервера после {retries} попыток (502 Bad Gateway).")
                    return [] 
            
            response.raise_for_status()  # выбрасывает исключение при других ошибочных статусах (кроме 502, который мы обработали)
            clients = response.json()  # ожидается, что API вернет JSON в виде списка словарей
            return clients
        except requests.RequestException as e:
            print(f"Ошибка при запросе к API: {e}")
            if attempt < retries - 1:
                print(f"Повторная попытка через {delay_seconds} сек...")
                time.sleep(delay_seconds)
            else:
                print(f"Не удалось получить данные с сервера после {retries} попыток (RequestException).")
                return []
    return [] # На случай, если цикл завершится без return (хотя не должен при такой логике)


# Функция для запуска процесса обработки заявки
def run_process(client):
    client_id = client.get('id')  # предполагается, что идентификатор заявки хранится в поле 'id'
    print(f"Запуск обработки заявки с ID: {client_id}")
    # Запуск файла main_new.py в новом консольном окне с передачей client_id в качестве аргумента
    subprocess.Popen([sys.executable, 'main.py', f'Z{str(client_id)}'],
                     start_new_session=True,
                     creationflags=subprocess.CREATE_NEW_CONSOLE)


def run_all(clients):
    """Запустить все процессы сразу."""
    for client in clients:
        run_process(client)


def run_in_batches(clients, batch_size, delay):
    """Запуск процессов партиями: batch_size штук, задержка delay секунд между пачками."""
    total = len(clients)
    for i in range(0, total, batch_size):
        batch = clients[i:i + batch_size]
        for client in batch:
            run_process(client)
        # Если эта не последняя пачка, делаем задержку
        if i + batch_size < total:
            print(f"Задержка {delay} секунд перед запуском следующей пачки...")
            time.sleep(delay)


if __name__ == '__main__':
    # Получаем список заявок через API
    clients = get_clients()

    if not clients:
        print("Список заявок пуст или произошла ошибка при получении данных.")
        sys.exit(1)

    # Фильтруем заявки, оставляя только те, у которых status == 'INWORK'
    try:
        clients_in_work = []
        for client_id, client_data in clients.items():
            if not isinstance(client_data, dict):
                print(f"Элемент с ключом {client_id} не является словарем: {client_data}")
                continue
            # Используем get() с указанием значения по умолчанию None.
            # Это позволяет избежать ошибки KeyError, если ключа 'status' нет.
            if client_data.get('status', None) == 'INWORK':
                clients_in_work.append(client_data)

    except:
        traceback.print_exc()
        pass

    if not clients_in_work:
        print("Нет заявок со статусом 'INWORK'.")
        sys.exit(0)

    print(f"Найдено {len(clients_in_work)} заявок со статусом 'INWORK'.")

    # Предлагаем выбрать режим запуска
    print("Выберите режим запуска:")
    print("1) Запустить всех клиентов сразу")
    print("2) Запускать пачками по 10 штук раз в 20 секунд")
    print("3) Указать количество в пачке и интервал между ними вручную (с выбором городов)")

    mode = input("Введите номер режима (1, 2 или 3): ").strip()

    if mode == "1":
        print("Запускаем всех клиентов сразу...")
        run_all(clients_in_work)
    elif mode == "2":
        print("Запускаем пачками по 10 штук раз в 20 секунд...")
        run_in_batches(clients_in_work, batch_size=10, delay=20)
    elif mode == "3":
        try:
            batch_input = input("Введите количество клиентов в пачке: ").strip()
            delay_input = input("Введите интервал между запусками (в секундах): ").strip()
            batch_size = int(batch_input)
            delay = int(delay_input)

            # --- Измененная логика выбора городов (с английскими названиями) ---
            available_cities = {} # Словарь для хранения {ID: Название} найденных городов
            print("Анализ городов в заявках 'INWORK'...")
            # Убран отладочный вывод словаря
            for client_data in clients_in_work:
                # Получаем НАЗВАНИЕ города из поля 'visaCenter' (ожидается английское)
                city_name_from_api = client_data.get('visaCenter')
                if city_name_from_api:
                    # Приводим к нижнему регистру для поиска в reverse_mapping
                    city_name_lower = city_name_from_api.strip().lower()
                    # ВОЗВРАЩАЕМ Отладочный вывод перед поиском
                    print(f"DEBUG: Поиск ID для city_name_lower: '{city_name_lower}' (repr: {repr(city_name_lower)})")
                    # Ищем ID по английскому названию в обратном маппинге
                    city_id = visa_centers_reverse_mapping.get(city_name_lower)

                    if city_id is not None:
                        # Если ID найден, добавляем в словарь доступных городов
                        # Используем название из основного mapping (теперь английское)
                        if city_id not in available_cities:
                             proper_city_name = visa_centers_mapping.get(city_id, city_name_from_api.strip())
                             available_cities[city_id] = proper_city_name
                    # else:
                         # Раскомментируйте для отладки, если города снова не находятся
                         # print(f"Предупреждение: Название города '{city_name_from_api}' (ключ: '{city_name_lower}') из API не найдено в словаре visa_centers_reverse_mapping.")

            if not available_cities:
                print("Не найдено известных городов (с ID в словаре) в заявках INWORK.")
                # Продолжаем без фильтрации по городам
                selected_clients = clients_in_work
            else:
                print("Доступные города (ID и Название) для заявок 'INWORK':")
                # Сортируем по ID для удобства отображения
                for city_id, city_name in sorted(available_cities.items()):
                    print(f"- {city_id}: {city_name}")

                city_choice_input = input("Введите ID городов через запятую для запуска (например, 7,14), "
                                        "ALL для всех, или оставьте пустым для всех: ").strip().upper()

                selected_clients = []
                if not city_choice_input or city_choice_input == 'ALL':
                    print("Выбраны все доступные города.")
                    selected_clients = clients_in_work
                else:
                    try:
                        # Обрабатываем ввод пользователя: получаем выбранные ID
                        selected_city_ids = {int(id_str.strip()) for id_str in city_choice_input.split(',') if id_str.strip()}
                        print(f"Выбраны города с ID: {selected_city_ids}")

                        # Получаем НАЗВАНИЯ городов, соответствующие выбранным ID, для фильтрации заявок
                        selected_city_names_lower = {
                            visa_centers_mapping[city_id].lower()
                            for city_id in selected_city_ids
                            if city_id in visa_centers_mapping # Убедимся, что ID есть в маппинге
                        }

                        if not selected_city_names_lower:
                             print("Предупреждение: Не удалось найти названия для введенных ID в словаре. Проверьте правильность ID.")
                             selected_clients = [] # Не запускаем ничего, если ID невалидны
                        else:
                            print(f"Фильтруем заявки по названиям: {', '.join(name.capitalize() for name in selected_city_names_lower)}")
                            selected_clients = [
                                client for client in clients_in_work
                                # Сравниваем НАЗВАНИЕ города из заявки (приведенное к нижнему регистру)
                                # с НАЗВАНИЯМИ, соответствующими выбранным ID
                                if client.get('visaCenter') and client.get('visaCenter').strip().lower() in selected_city_names_lower
                            ]

                            if not selected_clients:
                                 print("Предупреждение: Не найдено заявок 'INWORK' для выбранных городов (по названиям).")
                            else:
                                 print(f"Найдено {len(selected_clients)} заявок для выбранных городов.")

                    except ValueError:
                        # Ошибка при преобразовании ID в число
                        print("Ошибка: Неверный формат ввода ID городов. Вводите только числа через запятую. Запускаем для всех доступных городов.")
                        selected_clients = clients_in_work # Возвращаемся к запуску всех, как запасной вариант
                    except KeyError as e:
                        # Ошибка - введенный ID отсутствует в visa_centers_mapping
                        print(f"Ошибка: Введенный ID города ({e}) отсутствует в словаре visa_centers_mapping. Невозможно отфильтровать.")
                        selected_clients = [] # Не запускаем ничего в случае ошибки ключа

            # --- Конец измененного кода ---

            if selected_clients: # Запускаем только если есть клиенты после фильтрации
                print(f"Запускаем {len(selected_clients)} клиентов пачками по {batch_size} штук раз в {delay} секунд...")
                run_in_batches(selected_clients, batch_size=batch_size, delay=delay)
            else:
                print("Нет клиентов для запуска после фильтрации.")

        except ValueError:
            print("Неверный формат ввода для размера пачки или интервала. Ожидались числа.")
    else:
        print("Неверный выбор режима запуска.")
        sys.exit(1)

    print("Все процессы запущены. Закройте это окно при необходимости.")
