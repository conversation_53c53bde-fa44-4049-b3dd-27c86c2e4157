import time
from datetime import datetime
import statistics
import logging

# Попытка импортировать ntplib для NTP, опционально
try:
    import ntplib
    NTP_AVAILABLE = True
except ImportError:
    NTP_AVAILABLE = False
    print("⚠️ ntplib не найден. NTP синхронизация будет пропущена.")

# Константы для NTP и измерения задержки
NTP_SERVERS = ['pool.ntp.org', 'time.google.com', 'time.cloudflare.com']
NTP_TIMEOUT = 3  # секунды
LATENCY_TEST_COUNT = 2
DEFAULT_REQUEST_TIMEOUT = 10  # секунды
CF_BUFFER_MS = 100  # миллисекунды для CloudFlare
NON_CF_BUFFER_MS = 50  # миллисекунды для обычных серверов
USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'

class SimpleTimer:
    def __init__(self):
        self.time_offset = 0
        if NTP_AVAILABLE:
            self.sync_with_ntp()
        else:
            print("🕒 NTP пропущен. Используется локальное время.")

    def sync_with_ntp(self):
        """Синхронизация с NTP сервером"""
        if not NTP_AVAILABLE:
            return False
        print("🕐 Синхронизация с NTP...")
        for server in NTP_SERVERS:
            try:
                client = ntplib.NTPClient()
                response = client.request(server, version=3, timeout=NTP_TIMEOUT)
                ntp_time = response.tx_time
                # Простейший расчет offset
                self.time_offset = ntp_time - time.time()
                print(f"✅ NTP {server}, смещение: {self.time_offset:.3f}s")
                return True
            except Exception as e:
                print(f"❌ Ошибка NTP {server}: {e}")
        print("💥 Не удалось синхронизироваться с NTP-серверами.")
        return False

    def get_precise_time(self):
        """Возвращает время с учетом NTP-смещения"""
        return time.time() + self.time_offset

    def get_precise_datetime(self):
        """Возвращает datetime с учетом NTP-смещения"""
        return datetime.fromtimestamp(self.get_precise_time())


def test_server_latency(session, url, count=LATENCY_TEST_COUNT, json_payload=None, headers=None):
    """Тестирование задержки до сервера и вычисление рекомендуемого опережения"""
    latencies = []
    headers_list = []
    for i in range(count):
        try:
            start = time.perf_counter()
            if json_payload is not None:
                response = session.post(url, json=json_payload, headers=headers, timeout=DEFAULT_REQUEST_TIMEOUT)
            else:
                response = session.get(url, timeout=DEFAULT_REQUEST_TIMEOUT, headers=headers)
            response.raise_for_status()
            end = time.perf_counter()
            latency_ms = (end - start) * 1000
            latencies.append(latency_ms)
            headers_list.append(response.headers)
            time.sleep(0.1)
        except Exception:
            continue
    if not latencies:
        logging.warning(f"Все {count} попыток теста задержки неуспешны, возвращаю 0.5s")
        return 0.5  # дефолт 0.5s если все запросы не успешны
    avg = statistics.mean(latencies)
    std_dev = statistics.stdev(latencies) if len(latencies) > 1 else 0
    p95 = statistics.quantiles(latencies, n=20)[-1] if len(latencies) >= 20 else max(latencies)
    base = max(p95, avg + 2 * std_dev)
    cf_detected = any('cf-ray' in h for h in headers_list)
    buffer_ms = CF_BUFFER_MS if cf_detected else NON_CF_BUFFER_MS
    total_ms = base + buffer_ms
    # Логирование деталей расчета
    logging.info(f"Статистика теста задержки (ms): avg={avg:.2f}, std_dev={std_dev:.2f}, p95={p95:.2f}")
    logging.info(f"Базовое опережение (max(p95, avg+2σ)) = {base:.2f} ms")
    logging.info(f"Буфер = {buffer_ms} ms ({'CF' if cf_detected else 'non-CF'})")
    logging.info(f"Рекомендованное опережение: {total_ms:.2f} ms ({total_ms/1000:.3f} s)")
    return total_ms / 1000  # возвращаем в секундах 