import traceback
import requests

def update_order_status(order_id, status=None, internal_status=None, api_key='29140812490ksadkljals'):
    """
    Функция для обновления статуса заказа через API.

    :param order_id: ID заказа для обновления
    :param status: Новый статус заказа (опционально)
    :param internal_status: Новый внутренний статус заказа (опционально)
    :param api_key: API ключ для авторизации
    :param base_url: Базовый URL API
    :return: True если обновление успешно, иначе False
    """
    #url = f"http://127.0.0.1:8000/api/orders/{order_id}/update-status/"
    url = f"https://new.fast-bot.ru/api/orders/{order_id}/update-status/"
    # Формируем URL для запроса

    # Подготавливаем данные для отправки
    payload = {}
    if status:
        payload['status'] = status
    if internal_status:
        payload['internal_status'] = internal_status

    # Если нет данных для обновления, выходим
    if not payload:
        print("Необходимо указать хотя бы один параметр: status или internal_status")
        return False

    # Добавляем API ключ в параметры запроса
    params = {
        "api_key": api_key
    }

    # Отправляем PUT запрос
    try:
        response = requests.put(url, json=payload, params=params)

        # Проверяем статус ответа
        if response.status_code == 200:
            result = response.json()
            print("Статус заказа успешно обновлен:")
            print(f"Новый статус: {result.get('status')}")
            print(f"Новый внутренний статус: {result.get('internal_status')}")
            return True
        else:
            print(f"Ошибка при обновлении: {response.json().get('detail', 'Неизвестная ошибка')}")
            return False
    except requests.RequestException as e:
        print(f"Ошибка при отправке запроса: {e}")
        return False

def send_slack_error(error_message, customer_id=None, log_file=None, stack_trace=None):
    slack_webhook_url = '*********************************************************************************'

    if stack_trace is None:
        stack_trace = traceback.format_exc()

    payload = {
        'text': f'Error: {error_message}'
    }

    if customer_id:
        payload['text'] += f'\nCustomer ID: {customer_id}'

    if log_file:
        payload['text'] += f'\nLog file: {log_file}'

    if stack_trace and stack_trace != 'None\n':
        payload['text'] += f'\n\nТрассировка стека:\n```{stack_trace}```'
    else:
        payload['text'] += '\n\nТрассировка стека недоступна.'

    response = requests.post(slack_webhook_url, json=payload)
    if response.status_code != 200:
        print(f'Не удалось отправить сообщение в Slack. Код состояния: {response.status_code}')

def get_order_details(order_id, logger):
    api_url = f"https://new.fast-bot.ru/api/orders/{order_id}/?api_key=29140812490ksadkljals"
    params = {
        'token': 'aslkdjasdlk2392i39203i2!!dkjadkjasdkjlk'
    }

    try:
        logger.info(f"Получение деталей заказа {order_id} с API...")
        response = requests.get(api_url, params=params)
        response.raise_for_status()
        logger.info(f"Ответ API получен успешно.")

        server_resp = response.json()
        data = server_resp[str(order_id)]

        return data

    except requests.RequestException as e:
        logger.error(f"Ошибка при получении данных заказа {order_id}: {e}", exc_info=True)
        try:
            send_slack_error(f"Критическая ошибка при получении данных заказа {order_id}",
                             customer_id=order_id,
                             stack_trace=traceback.format_exc())
        except Exception as slack_e:
            logger.error(f"Не удалось отправить сообщение в Slack: {slack_e}")
        return None
    except KeyError as e:
        logger.error(f"Ошибка: Заказ {order_id} не найден в ответе API. Ответ: {server_resp}", exc_info=True)
        try:
            send_slack_error(f"Критическая ошибка: Заказ {order_id} не найден в ответе API.",
                             customer_id=order_id,
                             stack_trace=traceback.format_exc())
        except Exception as slack_e:
            logger.error(f"Не удалось отправить сообщение в Slack: {slack_e}")
        return None