from curl_cffi import requests
import json
from functions.ProxyFamilyIntegration import ProxyFamilyProxy
from functions.essentials import update_order_status


def test_login_creds(order_data, proxy_str=None):
    if proxy_str:
        proxies = {
            'http': f'http://{proxy_str}',
            'https': f'http://{proxy_str}'
        }
    else:
        proxies = None
    """Test login credentials from order data"""
    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"
    headers = {
        "User-Agent": user_agent,
        "Accept": "application/json, text/plain, */*",
        "Origin": "https://atlantis-abs-uk-ru.vfsglobal.com",
        "Referer": "https://atlantis-abs-uk-ru.vfsglobal.com/"
    }
    auth_url = "https://keycloak.vfsglobal.com/realms/vfs_realm/protocol/openid-connect/token"

    try:
        applicant_data = json.loads(order_data['applicants'])[0]
        auth_data = {
            "grant_type": "password",
            "client_id": "test-cors",
            "username": applicant_data['gwf_number'],
            "password": applicant_data['uk_email'],
            "client_secret": "YN4f3a1IAmKVTc4auZKFhZaNcQhbTL2m"
        }
    except (KeyError, json.JSONDecodeError, IndexError) as e:
        print(f"❌ Error parsing applicant data: {e}")
        return False

    try:
        response = requests.post(
            auth_url,
            data=auth_data,
            headers=headers,
            timeout=30,
            impersonate='firefox135',
            proxies=proxies
        )
        if response.status_code == 200:
            token_data = response.json()
            print(f"✅ Login successful for user: {auth_data.get('username', 'Unknown')}")
            return True
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False


def checker(order_data):
    """Check and validate order data"""
    order_id = order_data.get('id', 'Unknown')
    print(f'Attempting to Login with user submitted data for order {order_id}')

    # Get proxy if exists
    proxy_str = order_data.get('proxy')
    if proxy_str and proxy_str != '0':
        print(f"Using proxy: {proxy_str}")
    else:
        print("No proxy provided, using direct connection")
        proxy_str = None

    # Test the login credentials
    login_result = test_login_creds(order_data, proxy_str)

    if login_result:
        print(f"✅ Order {order_id}: Credentials are valid")
        print('Checking if proxy exists...')
        if not order_data.get('proxy') or order_data.get('proxy') == '0':
            print(f"❌ Proxy does not exist for order {order_id}")
            print('Buying proxy for order...')
            proxy = ProxyFamilyProxy()
            purchased = proxy.buy_proxy()
            if purchased:
                print(f"✅ purchased proxy for order {order_id} getting the details of it")
                proxy_string = proxy.get_proxystr()
                proxy.UpdateProxyOnSite(order_id, proxy_string)
                print('Proxy added to order,testing proxy capabillity')
                proxy_result = test_login_creds(order_data, proxy_string)
                if proxy_result:
                    update_order_status(order_id, status='INWORK', internal_status='READY')
                else:
                    update_order_status(order_id, status='INWORK', internal_status='PROXY_CHECK_FAILED')


            else:
                print(f"❌ Unable to purchase proxy for order {order_id}")
                update_order_status(order_id, status='ONCHECK', internal_status='PROXY_PURCHASE_FAILED')
        else:
            print(f"✅ Order {order_id}: Proxy already exists, credentials validated")
            update_order_status(order_id, status='INWORK', internal_status='READY')

        return {"status": "success", "order_id": order_id, "message": "Credentials validated"}
    else:
        print(f"❌ Order {order_id}: Credentials are invalid")
        update_order_status(order_id, status='ONCHECK', internal_status='INVALID_AUTH_DATA')
        return {"status": "failed", "order_id": order_id, "message": "Invalid credentials"}


def main():
    order_id = input('Enter order ID: ')
    api_url = f'https://new.fast-bot.ru/api/orders/{order_id}/?api_key=29140812490ksadkljals&format=json'
    try:
        print('Requesting data from API...')
        resp = requests.get(api_url)
        resp.raise_for_status()
        try:
            data = resp.json()[order_id]
            if data['country'] != 'United Kingdom' or not data['status'] == 'ONCHECK':
                if not data['status'] == 'ONCHECK':
                    print(f"❌ Order {order_id}: Status is not ONCHECK")
                    update_order_status(order_id, status='ONCHECK', internal_status='INVALID_STATUS')
                    return {"status": "failed", "order_id": order_id, "message": "Invalid status"}
                print(f"❌ Order {order_id}: Country is not United Kingdom")
                update_order_status(order_id, status='ONCHECK', internal_status='INVALID_COUNTRY')
                return {"status": "failed", "order_id": order_id, "message": "Invalid country"}
            print('Data received successfully.')
            print(json.dumps(data, indent=2))
            result = checker(data)

            if result:
                print(f"Checker result: {result}")
            else:
                print("No result from checker")
        except Exception as e:
            print(f'Unable to get order data: {e}')
            # Update status for data parsing errors
            try:
                update_order_status(order_id, status='ONCHECK', internal_status='DATA_PARSING_ERROR')
            except Exception as status_error:
                print(f'Failed to update order status: {status_error}')


    except Exception as e:
        print(f'Error: {e}')
        # Update status for API request errors
        try:
            update_order_status(order_id, status='ONCHECK', internal_status='API_REQUEST_ERROR')
        except Exception as status_error:
            print(f'Failed to update order status: {status_error}')


if __name__ == "__main__":
    main()