from curl_cffi import requests
import json
from functions.ProxyFamilyIntegration import ProxyF<PERSON>ly<PERSON><PERSON><PERSON>

def test_login_creds(order_data):
    """Test login credentials from order data"""
    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"
    headers = {
        "User-Agent": user_agent,
        "Accept": "application/json, text/plain, */*",
        "Origin": "https://atlantis-abs-uk-ru.vfsglobal.com",
        "Referer": "https://atlantis-abs-uk-ru.vfsglobal.com/"
    }
    auth_url = "https://keycloak.vfsglobal.com/realms/vfs_realm/protocol/openid-connect/token"
    applicant_data = json.loads(order_data['applicants'])[0]
    auth_data = {
        "grant_type": "password",
        "client_id": "test-cors",
        "username": applicant_data['gwf_number'],
        "password": applicant_data['uk_email'],
        "client_secret": "YN4f3a1IAmKVTc4auZKFhZaNcQhbTL2m"
    }

    try:
        response = requests.post(auth_url, data=auth_data, headers=headers, timeout=30,impersonate='firefox135')
        if response.status_code == 200:
            token_data = response.json()
            print(f"✅ Login successful for user: {auth_data.get('username', 'Unknown')}")
            return True
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False


def checker(order_data):
    """Check and validate order data"""
    order_id = order_data.get('id', 'Unknown')
    print(f'Attempting to Login with user submitted data for order {order_id}')

    # Test the login credentials
    login_result = test_login_creds(order_data)

    if login_result:
        print(f"✅ Order {order_id}: Credentials are valid")
        return {"status": "success", "order_id": order_id, "message": "Credentials validated"}
    else:
        print(f"❌ Order {order_id}: Credentials are invalid")
        return {"status": "failed", "order_id": order_id, "message": "Invalid credentials"}


def main():
    order_id = input('Enter order ID: ')
    api_url = f'https://new.fast-bot.ru/api/orders/{order_id}/?api_key=29140812490ksadkljals&format=json'
    try:
        print('Requesting data from API...')
        resp = requests.get(api_url)
        resp.raise_for_status()
        try:
            data = resp.json()[order_id]
            print('Data received successfully.')
            print(json.dumps(data, indent=2))
            result = checker(data)

            if result:
                print(f"Checker result: {result}")
            else:
                print("No result from checker")
        except Exception as e:
            print(f'Unable to get order data: {e}')


    except Exception as e:
        print(f'Error: {e}')


if __name__ == "__main__":
    main()